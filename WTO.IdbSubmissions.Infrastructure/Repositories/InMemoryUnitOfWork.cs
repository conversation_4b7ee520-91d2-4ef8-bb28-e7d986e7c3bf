using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Persistence.Common;

namespace WTO.IdbSubmissions.Infrastructure.Repositories;

/// <summary>
/// In-memory Unit of Work implementation for testing and development
/// This will be replaced with Entity Framework Core implementation in production
/// </summary>
public class InMemoryUnitOfWork : UnitOfWork
{
    private readonly Dictionary<Type, object> _repositories = new();
    private readonly object _lock = new();

    /// <summary>
    /// Saves all changes made in this unit of work to the underlying data store
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The number of state entries written to the underlying data store</returns>
    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // In-memory implementation doesn't need to save changes
        // Changes are immediately persisted to the in-memory collections
        return Task.FromResult(1);
    }

    /// <summary>
    /// Begins a new transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transaction object</returns>
    public override Task<ITransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        // Return a no-op transaction for in-memory implementation
        return Task.FromResult<ITransaction>(new InMemoryTransaction());
    }

    /// <summary>
    /// Discards all changes made in this unit of work
    /// </summary>
    public override void RejectChanges()
    {
        // In-memory implementation doesn't support rejecting changes
        // This would require implementing a more sophisticated change tracking mechanism
    }

    /// <summary>
    /// Creates a repository instance for the specified entity type
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <returns>Repository instance</returns>
    protected override IRepository<T> CreateRepository<T>()
    {
        lock (_lock)
        {
            var type = typeof(T);
            
            if (_repositories.ContainsKey(type))
            {
                return (IRepository<T>)_repositories[type];
            }

            var repository = new InMemoryRepository<T>();
            _repositories.Add(type, repository);
            return repository;
        }
    }

    /// <summary>
    /// Disposes the unit of work
    /// </summary>
    /// <param name="disposing">Whether disposing is in progress</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            lock (_lock)
            {
                _repositories.Clear();
            }
        }
        base.Dispose(disposing);
    }
}

/// <summary>
/// In-memory transaction implementation (no-op)
/// </summary>
public class InMemoryTransaction : Transaction
{
    /// <summary>
    /// Commits the transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public override Task CommitAsync(CancellationToken cancellationToken = default)
    {
        // No-op for in-memory implementation
        return Task.CompletedTask;
    }

    /// <summary>
    /// Rolls back the transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public override Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        // No-op for in-memory implementation
        return Task.CompletedTask;
    }
}
