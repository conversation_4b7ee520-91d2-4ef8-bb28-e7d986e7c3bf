using System.Linq.Expressions;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Infrastructure.Common;

namespace WTO.IdbSubmissions.Infrastructure.Repositories;

/// <summary>
/// In-memory repository implementation for testing and development
/// This will be replaced with Entity Framework Core implementation in production
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public class InMemoryRepository<T> : BaseRepository<T> where T : BaseEntity
{
    private readonly List<T> _entities = new();
    private readonly object _lock = new();

    /// <summary>
    /// Gets an entity by its identifier
    /// </summary>
    /// <param name="id">The entity identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The entity if found, null otherwise</returns>
    public override Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var entity = _entities.FirstOrDefault(e => e.Id == id);
            return Task.FromResult(entity);
        }
    }

    /// <summary>
    /// Gets all entities
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of all entities</returns>
    public override Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var entities = _entities.Where(e => !e.IsDeleted).ToList();
            return Task.FromResult<IEnumerable<T>>(entities);
        }
    }

    /// <summary>
    /// Gets all entities matching a predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of entities matching the predicate</returns>
    public override Task<IEnumerable<T>> GetAllAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var compiled = predicate.Compile();
            var entities = _entities.Where(compiled).ToList();
            return Task.FromResult<IEnumerable<T>>(entities);
        }
    }

    /// <summary>
    /// Finds entities matching the specified predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of entities matching the predicate</returns>
    public override Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var compiled = predicate.Compile();
            var entities = _entities.Where(e => !e.IsDeleted && compiled(e)).ToList();
            return Task.FromResult<IEnumerable<T>>(entities);
        }
    }

    /// <summary>
    /// Gets the first entity matching the predicate or null if not found
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>First entity matching the predicate or null</returns>
    public override Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var compiled = predicate.Compile();
            var entity = _entities.FirstOrDefault(compiled);
            return Task.FromResult(entity);
        }
    }

    /// <summary>
    /// Checks if any entity matches the predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if any entity matches, false otherwise</returns>
    public override Task<bool> AnyAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var compiled = predicate.Compile();
            var exists = _entities.Any(compiled);
            return Task.FromResult(exists);
        }
    }

    /// <summary>
    /// Gets the count of entities matching the predicate
    /// </summary>
    /// <param name="predicate">The search predicate (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Count of entities</returns>
    public override Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            if (predicate == null)
            {
                var count = _entities.Count(e => !e.IsDeleted);
                return Task.FromResult(count);
            }
            else
            {
                var compiled = predicate.Compile();
                var count = _entities.Count(compiled);
                return Task.FromResult(count);
            }
        }
    }

    /// <summary>
    /// Adds a new entity
    /// </summary>
    /// <param name="entity">The entity to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The added entity</returns>
    public override Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        lock (_lock)
        {
            if (entity.Id == Guid.Empty)
            {
                entity.Id = Guid.NewGuid();
            }

            entity.CreatedAt = DateTime.UtcNow;
            _entities.Add(entity);
            return Task.FromResult(entity);
        }
    }

    /// <summary>
    /// Adds multiple entities
    /// </summary>
    /// <param name="entities">The entities to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public override Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities));

        lock (_lock)
        {
            foreach (var entity in entities)
            {
                if (entity.Id == Guid.Empty)
                {
                    entity.Id = Guid.NewGuid();
                }
                entity.CreatedAt = DateTime.UtcNow;
                _entities.Add(entity);
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Updates an existing entity
    /// </summary>
    /// <param name="entity">The entity to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public override Task UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        lock (_lock)
        {
            var existingIndex = _entities.FindIndex(e => e.Id == entity.Id);
            if (existingIndex >= 0)
            {
                entity.ModifiedAt = DateTime.UtcNow;
                _entities[existingIndex] = entity;
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Updates multiple existing entities
    /// </summary>
    /// <param name="entities">The entities to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public override Task UpdateRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities));

        lock (_lock)
        {
            foreach (var entity in entities)
            {
                var existingIndex = _entities.FindIndex(e => e.Id == entity.Id);
                if (existingIndex >= 0)
                {
                    entity.ModifiedAt = DateTime.UtcNow;
                    _entities[existingIndex] = entity;
                }
            }
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Removes an entity
    /// </summary>
    /// <param name="entity">The entity to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public override Task RemoveAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        lock (_lock)
        {
            _entities.RemoveAll(e => e.Id == entity.Id);
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Removes multiple entities
    /// </summary>
    /// <param name="entities">The entities to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public override Task RemoveRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities));

        lock (_lock)
        {
            var idsToRemove = entities.Select(e => e.Id).ToHashSet();
            _entities.RemoveAll(e => idsToRemove.Contains(e.Id));
            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Gets a paginated list of entities matching a predicate with sorting
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="skip">Number of items to skip</param>
    /// <param name="take">Number of items to take</param>
    /// <param name="orderBy">Sort expression</param>
    /// <param name="descending">Whether to sort in descending order</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated collection of entities</returns>
    public override Task<IEnumerable<T>> GetPagedAsync(
        Expression<Func<T, bool>> predicate,
        int skip,
        int take,
        Expression<Func<T, object>> orderBy,
        bool descending = false,
        CancellationToken cancellationToken = default)
    {
        lock (_lock)
        {
            var compiledPredicate = predicate.Compile();
            var compiledOrderBy = orderBy.Compile();
            
            var query = _entities.Where(compiledPredicate);
            
            if (descending)
            {
                query = query.OrderByDescending(compiledOrderBy);
            }
            else
            {
                query = query.OrderBy(compiledOrderBy);
            }
            
            var result = query.Skip(skip).Take(take).ToList();
            return Task.FromResult<IEnumerable<T>>(result);
        }
    }
}
