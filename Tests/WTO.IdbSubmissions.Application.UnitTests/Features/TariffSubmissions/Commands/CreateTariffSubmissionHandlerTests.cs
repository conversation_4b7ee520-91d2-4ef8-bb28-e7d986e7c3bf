using FluentAssertions;
using Moq;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;
using Xunit;

namespace WTO.IdbSubmissions.Application.UnitTests.Features.TariffSubmissions.Commands;

/// <summary>
/// Unit tests for CreateTariffSubmissionHandler
/// </summary>
public class CreateTariffSubmissionHandlerTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IRepository<TariffSubmission>> _mockRepository;
    private readonly CreateTariffSubmissionHandler _handler;

    public CreateTariffSubmissionHandlerTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockRepository = new Mock<IRepository<TariffSubmission>>();
        _mockUnitOfWork.Setup(x => x.Repository<TariffSubmission>()).Returns(_mockRepository.Object);
        _handler = new CreateTariffSubmissionHandler(_mockUnitOfWork.Object);
    }

    [Fact]
    public async Task HandleAsync_ValidCommand_ShouldCreateTariffSubmission()
    {
        // Arrange
        var command = new CreateTariffSubmissionCommand
        {
            WTOCountryCode = "USA",
            Year = 2024,
            Currency = "USD",
            TariffSubmissionOrigin = TariffSubmissionOrigin.Government,
            OriginalLanguage = WTOLanguage.English,
            PreferentialTariffs = true,
            BeneficiaryList = false,
            OtherDutiesAndCharges = true,
            DutiesApplicableFrom = new DateOnly(2024, 1, 1),
            DutiesApplicableTo = new DateOnly(2024, 12, 31),
            Description = "Test submission",
            CreatedBy = "<EMAIL>"
        };

        _mockRepository.Setup(x => x.FirstOrDefaultAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<TariffSubmission, bool>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync((TariffSubmission?)null);

        _mockRepository.Setup(x => x.AddAsync(It.IsAny<TariffSubmission>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((TariffSubmission submission) => submission);

        _mockUnitOfWork.Setup(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(1);

        // Act
        var result = await _handler.HandleAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.WTOCountryCode.Should().Be("USA");
        result.Data.Year.Should().Be(2024);
        result.Data.Status.Should().Be(SubmissionStatus.Draft);
        result.Data.CreatedBy.Should().Be("<EMAIL>");

        _mockRepository.Verify(x => x.AddAsync(It.IsAny<TariffSubmission>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_InvalidCountryCode_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateTariffSubmissionCommand
        {
            WTOCountryCode = "INVALID", // Invalid country code
            Year = 2024,
            Currency = "USD",
            TariffSubmissionOrigin = TariffSubmissionOrigin.Government,
            OriginalLanguage = WTOLanguage.English,
            PreferentialTariffs = true,
            BeneficiaryList = false,
            OtherDutiesAndCharges = true,
            DutiesApplicableFrom = new DateOnly(2024, 1, 1),
            DutiesApplicableTo = new DateOnly(2024, 12, 31)
        };

        // Act
        var result = await _handler.HandleAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(error => error.Contains("WTO Country Code must be exactly 3 characters"));

        _mockRepository.Verify(x => x.AddAsync(It.IsAny<TariffSubmission>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task HandleAsync_DuplicateSubmission_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateTariffSubmissionCommand
        {
            WTOCountryCode = "USA",
            Year = 2024,
            Currency = "USD",
            TariffSubmissionOrigin = TariffSubmissionOrigin.Government,
            OriginalLanguage = WTOLanguage.English,
            PreferentialTariffs = true,
            BeneficiaryList = false,
            OtherDutiesAndCharges = true,
            DutiesApplicableFrom = new DateOnly(2024, 1, 1),
            DutiesApplicableTo = new DateOnly(2024, 12, 31)
        };

        var existingSubmission = new TariffSubmission
        {
            WTOCountryCode = "USA",
            Year = 2024,
            Currency = "USD",
            TariffSubmissionOrigin = TariffSubmissionOrigin.Government,
            OriginalLanguage = WTOLanguage.English,
            PreferentialTariffs = true,
            BeneficiaryList = false,
            OtherDutiesAndCharges = true,
            DutiesApplicableFrom = new DateOnly(2024, 1, 1),
            DutiesApplicableTo = new DateOnly(2024, 12, 31)
        };

        _mockRepository.Setup(x => x.FirstOrDefaultAsync(
            It.IsAny<System.Linq.Expressions.Expression<Func<TariffSubmission, bool>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingSubmission);

        // Act
        var result = await _handler.HandleAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(error => error.Contains("already exists"));

        _mockRepository.Verify(x => x.AddAsync(It.IsAny<TariffSubmission>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task HandleAsync_InvalidDateRange_ShouldReturnFailure()
    {
        // Arrange
        var command = new CreateTariffSubmissionCommand
        {
            WTOCountryCode = "USA",
            Year = 2024,
            Currency = "USD",
            TariffSubmissionOrigin = TariffSubmissionOrigin.Government,
            OriginalLanguage = WTOLanguage.English,
            PreferentialTariffs = true,
            BeneficiaryList = false,
            OtherDutiesAndCharges = true,
            DutiesApplicableFrom = new DateOnly(2024, 12, 31), // After the "to" date
            DutiesApplicableTo = new DateOnly(2024, 1, 1)
        };

        // Act
        var result = await _handler.HandleAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(error => error.Contains("Duties applicable from date must be before duties applicable to date"));

        _mockRepository.Verify(x => x.AddAsync(It.IsAny<TariffSubmission>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
