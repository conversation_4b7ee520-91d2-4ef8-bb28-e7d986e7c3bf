using FluentAssertions;
using Moq;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;
using Xunit;

namespace WTO.IdbSubmissions.Application.UnitTests.Features.TariffSubmissions.Queries;

/// <summary>
/// Unit tests for GetTariffSubmissionHandler
/// </summary>
public class GetTariffSubmissionHandlerTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IRepository<TariffSubmission>> _mockRepository;
    private readonly GetTariffSubmissionHandler _handler;

    public GetTariffSubmissionHandlerTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockRepository = new Mock<IRepository<TariffSubmission>>();
        _mockUnitOfWork.Setup(x => x.Repository<TariffSubmission>()).Returns(_mockRepository.Object);
        _handler = new GetTariffSubmissionHandler(_mockUnitOfWork.Object);
    }

    [Fact]
    public async Task HandleAsync_ValidId_ShouldReturnTariffSubmission()
    {
        // Arrange
        var submissionId = Guid.NewGuid();
        var tariffSubmission = new TariffSubmission
        {
            WTOCountryCode = "USA",
            Year = 2024,
            Status = SubmissionStatus.Draft,
            Currency = "USD",
            TariffSubmissionOrigin = TariffSubmissionOrigin.Government,
            OriginalLanguage = WTOLanguage.English,
            PreferentialTariffs = true,
            BeneficiaryList = false,
            OtherDutiesAndCharges = true,
            DutiesApplicableFrom = new DateOnly(2024, 1, 1),
            DutiesApplicableTo = new DateOnly(2024, 12, 31),
            Description = "Test submission"
        };

        // Use reflection to set the Id since it has a protected setter
        var idProperty = typeof(TariffSubmission).BaseType!.GetProperty("Id");
        idProperty!.SetValue(tariffSubmission, submissionId);

        var query = new GetTariffSubmissionQuery
        {
            Id = submissionId,
            IncludeDeleted = false
        };

        _mockRepository.Setup(x => x.GetByIdAsync(submissionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tariffSubmission);

        // Act
        var result = await _handler.HandleAsync(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Id.Should().Be(submissionId);
        result.Data.WTOCountryCode.Should().Be("USA");
        result.Data.Year.Should().Be(2024);
        result.Data.Status.Should().Be(SubmissionStatus.Draft);

        _mockRepository.Verify(x => x.GetByIdAsync(submissionId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_InvalidId_ShouldReturnFailure()
    {
        // Arrange
        var query = new GetTariffSubmissionQuery
        {
            Id = Guid.Empty,
            IncludeDeleted = false
        };

        // Act
        var result = await _handler.HandleAsync(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(error => error.Contains("Tariff submission ID is required"));

        _mockRepository.Verify(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task HandleAsync_NonExistentId_ShouldReturnFailure()
    {
        // Arrange
        var submissionId = Guid.NewGuid();
        var query = new GetTariffSubmissionQuery
        {
            Id = submissionId,
            IncludeDeleted = false
        };

        _mockRepository.Setup(x => x.GetByIdAsync(submissionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((TariffSubmission?)null);

        // Act
        var result = await _handler.HandleAsync(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(error => error.Contains("No tariff submission found"));

        _mockRepository.Verify(x => x.GetByIdAsync(submissionId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_DeletedSubmissionWithoutIncludeDeleted_ShouldReturnFailure()
    {
        // Arrange
        var submissionId = Guid.NewGuid();
        var tariffSubmission = new TariffSubmission
        {
            WTOCountryCode = "USA",
            Year = 2024,
            Status = SubmissionStatus.Draft,
            Currency = "USD",
            TariffSubmissionOrigin = TariffSubmissionOrigin.Government,
            OriginalLanguage = WTOLanguage.English,
            PreferentialTariffs = true,
            BeneficiaryList = false,
            OtherDutiesAndCharges = true,
            DutiesApplicableFrom = new DateOnly(2024, 1, 1),
            DutiesApplicableTo = new DateOnly(2024, 12, 31)
        };

        // Mark as deleted
        tariffSubmission.SetDeleted("<EMAIL>");

        // Use reflection to set the Id
        var idProperty = typeof(TariffSubmission).BaseType!.GetProperty("Id");
        idProperty!.SetValue(tariffSubmission, submissionId);

        var query = new GetTariffSubmissionQuery
        {
            Id = submissionId,
            IncludeDeleted = false
        };

        _mockRepository.Setup(x => x.GetByIdAsync(submissionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tariffSubmission);

        // Act
        var result = await _handler.HandleAsync(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().Contain(error => error.Contains("has been deleted"));

        _mockRepository.Verify(x => x.GetByIdAsync(submissionId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task HandleAsync_DeletedSubmissionWithIncludeDeleted_ShouldReturnSuccess()
    {
        // Arrange
        var submissionId = Guid.NewGuid();
        var tariffSubmission = new TariffSubmission
        {
            WTOCountryCode = "USA",
            Year = 2024,
            Status = SubmissionStatus.Draft,
            Currency = "USD",
            TariffSubmissionOrigin = TariffSubmissionOrigin.Government,
            OriginalLanguage = WTOLanguage.English,
            PreferentialTariffs = true,
            BeneficiaryList = false,
            OtherDutiesAndCharges = true,
            DutiesApplicableFrom = new DateOnly(2024, 1, 1),
            DutiesApplicableTo = new DateOnly(2024, 12, 31)
        };

        // Mark as deleted
        tariffSubmission.SetDeleted("<EMAIL>");

        // Use reflection to set the Id
        var idProperty = typeof(TariffSubmission).BaseType!.GetProperty("Id");
        idProperty!.SetValue(tariffSubmission, submissionId);

        var query = new GetTariffSubmissionQuery
        {
            Id = submissionId,
            IncludeDeleted = true
        };

        _mockRepository.Setup(x => x.GetByIdAsync(submissionId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tariffSubmission);

        // Act
        var result = await _handler.HandleAsync(query, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Data.Should().NotBeNull();
        result.Data!.Id.Should().Be(submissionId);

        _mockRepository.Verify(x => x.GetByIdAsync(submissionId, It.IsAny<CancellationToken>()), Times.Once);
    }
}
