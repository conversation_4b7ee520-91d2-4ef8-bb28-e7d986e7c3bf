using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;

/// <summary>
/// Command to submit a tariff submission (change status from Draft to Submitted)
/// </summary>
public class SubmitTariffSubmissionCommand
{
    /// <summary>
    /// ID of the tariff submission to submit
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User submitting the tariff submission
    /// </summary>
    public string? SubmittedBy { get; set; }
}

/// <summary>
/// Response for SubmitTariffSubmission command
/// </summary>
public class SubmitTariffSubmissionResponse : TariffSubmissionDto
{
    // Inherits all properties from TariffSubmissionDto
}

/// <summary>
/// Handler for SubmitTariffSubmission command
/// </summary>
public class SubmitTariffSubmissionHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the SubmitTariffSubmissionHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public SubmitTariffSubmissionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the SubmitTariffSubmission command
    /// </summary>
    /// <param name="command">The command to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the submitted tariff submission response</returns>
    public async Task<Result<SubmitTariffSubmissionResponse>> HandleAsync(SubmitTariffSubmissionCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the command
            var validationResult = ValidateCommand(command);
            if (!validationResult.IsSuccess)
            {
                return Result<SubmitTariffSubmissionResponse>.Failure(validationResult.Errors);
            }

            // Get the existing tariff submission
            var repository = _unitOfWork.Repository<TariffSubmission>();
            var tariffSubmission = await repository.GetByIdAsync(command.Id, cancellationToken);

            if (tariffSubmission == null)
            {
                return Result<SubmitTariffSubmissionResponse>.Failure($"No tariff submission found with ID: {command.Id}");
            }

            if (tariffSubmission.IsDeleted)
            {
                return Result<SubmitTariffSubmissionResponse>.Failure("Cannot submit a deleted tariff submission");
            }

            // Check if submission can be submitted (only Draft status can be submitted)
            if (tariffSubmission.Status != SubmissionStatus.Draft)
            {
                return Result<SubmitTariffSubmissionResponse>.Failure($"Cannot submit tariff submission with status: {tariffSubmission.Status}. Only Draft submissions can be submitted.");
            }

            // Validate submission completeness
            var completenessValidation = ValidateSubmissionCompleteness(tariffSubmission);
            if (!completenessValidation.IsSuccess)
            {
                return Result<SubmitTariffSubmissionResponse>.Failure(completenessValidation.Errors);
            }

            // Update status and submission details
            tariffSubmission.Status = SubmissionStatus.Submitted;
            tariffSubmission.SubmittedAt = DateTime.UtcNow;
            tariffSubmission.SubmittedBy = command.SubmittedBy;
            tariffSubmission.SetModified(command.SubmittedBy);

            // Update in repository
            await repository.UpdateAsync(tariffSubmission, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Create response
            var response = MapToDto(tariffSubmission);

            return Result<SubmitTariffSubmissionResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<SubmitTariffSubmissionResponse>.Failure($"Failed to submit tariff submission: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the SubmitTariffSubmission command
    /// </summary>
    /// <param name="command">Command to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateCommand(SubmitTariffSubmissionCommand command)
    {
        var errors = new List<string>();

        // Validate ID
        if (command.Id == Guid.Empty)
            errors.Add("Tariff submission ID is required");

        // Validate SubmittedBy
        if (string.IsNullOrWhiteSpace(command.SubmittedBy))
            errors.Add("SubmittedBy is required when submitting a tariff submission");

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Validates that the submission is complete and ready for submission
    /// </summary>
    /// <param name="submission">The submission to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateSubmissionCompleteness(TariffSubmission submission)
    {
        var errors = new List<string>();

        // Check required fields
        if (string.IsNullOrWhiteSpace(submission.WTOCountryCode))
            errors.Add("WTO Country Code is required");

        if (string.IsNullOrWhiteSpace(submission.Currency))
            errors.Add("Currency is required");

        if (submission.Year <= 0)
            errors.Add("Valid year is required");

        // Check date range validity
        if (submission.DutiesApplicableFrom >= submission.DutiesApplicableTo)
            errors.Add("Duties applicable from date must be before duties applicable to date");

        // Check that at least one tariff type is selected
        if (!submission.PreferentialTariffs && !submission.BeneficiaryList && !submission.OtherDutiesAndCharges)
            errors.Add("At least one tariff type must be selected (Preferential Tariffs, Beneficiary List, or Other Duties and Charges)");

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Maps a TariffSubmission entity to SubmitTariffSubmissionResponse DTO
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <returns>Mapped DTO</returns>
    private static SubmitTariffSubmissionResponse MapToDto(TariffSubmission entity)
    {
        return new SubmitTariffSubmissionResponse
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy
        };
    }
}
