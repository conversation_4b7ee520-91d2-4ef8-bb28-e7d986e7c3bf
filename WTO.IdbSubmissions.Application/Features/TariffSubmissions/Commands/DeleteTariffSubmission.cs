using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;

/// <summary>
/// Command to delete a tariff submission (soft delete)
/// </summary>
public class DeleteTariffSubmissionCommand
{
    /// <summary>
    /// ID of the tariff submission to delete
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User performing the deletion
    /// </summary>
    public string? DeletedBy { get; set; }

    /// <summary>
    /// Whether to perform a hard delete instead of soft delete
    /// </summary>
    public bool HardDelete { get; set; } = false;
}

/// <summary>
/// Response for DeleteTariffSubmission command
/// </summary>
public class DeleteTariffSubmissionResponse
{
    /// <summary>
    /// ID of the deleted tariff submission
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// WTO Country Code of the deleted submission
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Year of the deleted submission
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// Date when the submission was deleted
    /// </summary>
    public DateTime DeletedAt { get; set; }

    /// <summary>
    /// User who deleted the submission
    /// </summary>
    public string? DeletedBy { get; set; }

    /// <summary>
    /// Whether this was a hard delete
    /// </summary>
    public bool WasHardDeleted { get; set; }
}

/// <summary>
/// Handler for DeleteTariffSubmission command
/// </summary>
public class DeleteTariffSubmissionHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the DeleteTariffSubmissionHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public DeleteTariffSubmissionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the DeleteTariffSubmission command
    /// </summary>
    /// <param name="command">The command to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the delete operation response</returns>
    public async Task<Result<DeleteTariffSubmissionResponse>> HandleAsync(DeleteTariffSubmissionCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the command
            var validationResult = ValidateCommand(command);
            if (!validationResult.IsSuccess)
            {
                return Result<DeleteTariffSubmissionResponse>.Failure(validationResult.Errors);
            }

            // Get the existing tariff submission
            var repository = _unitOfWork.Repository<TariffSubmission>();
            var tariffSubmission = await repository.GetByIdAsync(command.Id, cancellationToken);

            if (tariffSubmission == null)
            {
                return Result<DeleteTariffSubmissionResponse>.Failure($"No tariff submission found with ID: {command.Id}");
            }

            if (tariffSubmission.IsDeleted && !command.HardDelete)
            {
                return Result<DeleteTariffSubmissionResponse>.Failure("Tariff submission is already deleted");
            }

            // Check if submission can be deleted based on status
            if (tariffSubmission.Status == SubmissionStatus.Finalized)
            {
                return Result<DeleteTariffSubmissionResponse>.Failure("Cannot delete a finalized tariff submission");
            }

            // Prepare response data before deletion
            var response = new DeleteTariffSubmissionResponse
            {
                Id = tariffSubmission.Id,
                WTOCountryCode = tariffSubmission.WTOCountryCode,
                Year = tariffSubmission.Year,
                DeletedAt = DateTime.UtcNow,
                DeletedBy = command.DeletedBy,
                WasHardDeleted = command.HardDelete
            };

            // Perform deletion
            if (command.HardDelete)
            {
                // Hard delete - permanently remove from database
                await repository.RemoveAsync(tariffSubmission, cancellationToken);
            }
            else
            {
                // Soft delete - mark as deleted
                tariffSubmission.SetDeleted(command.DeletedBy);
                await repository.UpdateAsync(tariffSubmission, cancellationToken);
            }

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            return Result<DeleteTariffSubmissionResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<DeleteTariffSubmissionResponse>.Failure($"Failed to delete tariff submission: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the DeleteTariffSubmission command
    /// </summary>
    /// <param name="command">Command to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateCommand(DeleteTariffSubmissionCommand command)
    {
        var errors = new List<string>();

        // Validate ID
        if (command.Id == Guid.Empty)
            errors.Add("Tariff submission ID is required");

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }
}
