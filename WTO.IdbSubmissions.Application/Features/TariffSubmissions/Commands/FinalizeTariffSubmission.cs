using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;

/// <summary>
/// Command to finalize a tariff submission (change status from Accepted to Finalized)
/// </summary>
public class FinalizeTariffSubmissionCommand
{
    /// <summary>
    /// ID of the tariff submission to finalize
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User finalizing the tariff submission
    /// </summary>
    public string? FinalizedBy { get; set; }

    /// <summary>
    /// Optional notes about the finalization
    /// </summary>
    public string? FinalizationNotes { get; set; }
}

/// <summary>
/// Response for FinalizeTariffSubmission command
/// </summary>
public class FinalizeTariffSubmissionResponse : TariffSubmissionDto
{
    /// <summary>
    /// Date when the submission was finalized
    /// </summary>
    public DateTime FinalizedAt { get; set; }

    /// <summary>
    /// User who finalized the submission
    /// </summary>
    public string? FinalizedBy { get; set; }

    /// <summary>
    /// Notes about the finalization
    /// </summary>
    public string? FinalizationNotes { get; set; }
}

/// <summary>
/// Handler for FinalizeTariffSubmission command
/// </summary>
public class FinalizeTariffSubmissionHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the FinalizeTariffSubmissionHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public FinalizeTariffSubmissionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the FinalizeTariffSubmission command
    /// </summary>
    /// <param name="command">The command to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the finalized tariff submission response</returns>
    public async Task<Result<FinalizeTariffSubmissionResponse>> HandleAsync(FinalizeTariffSubmissionCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the command
            var validationResult = ValidateCommand(command);
            if (!validationResult.IsSuccess)
            {
                return Result<FinalizeTariffSubmissionResponse>.Failure(validationResult.Errors);
            }

            // Get the existing tariff submission
            var repository = _unitOfWork.Repository<TariffSubmission>();
            var tariffSubmission = await repository.GetByIdAsync(command.Id, cancellationToken);

            if (tariffSubmission == null)
            {
                return Result<FinalizeTariffSubmissionResponse>.Failure($"No tariff submission found with ID: {command.Id}");
            }

            if (tariffSubmission.IsDeleted)
            {
                return Result<FinalizeTariffSubmissionResponse>.Failure("Cannot finalize a deleted tariff submission");
            }

            // Check if submission can be finalized (only Accepted status can be finalized)
            if (tariffSubmission.Status != SubmissionStatus.Accepted)
            {
                return Result<FinalizeTariffSubmissionResponse>.Failure($"Cannot finalize tariff submission with status: {tariffSubmission.Status}. Only Accepted submissions can be finalized.");
            }

            // Validate finalization requirements
            var finalizationValidation = ValidateFinalizationRequirements(tariffSubmission);
            if (!finalizationValidation.IsSuccess)
            {
                return Result<FinalizeTariffSubmissionResponse>.Failure(finalizationValidation.Errors);
            }

            // Update status
            tariffSubmission.Status = SubmissionStatus.Finalized;
            tariffSubmission.SetModified(command.FinalizedBy);

            // Update description with finalization notes if provided
            if (!string.IsNullOrWhiteSpace(command.FinalizationNotes))
            {
                var finalizationNote = $"[FINALIZED {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC by {command.FinalizedBy}]: {command.FinalizationNotes.Trim()}";
                
                if (string.IsNullOrWhiteSpace(tariffSubmission.Description))
                {
                    tariffSubmission.Description = finalizationNote;
                }
                else
                {
                    tariffSubmission.Description += Environment.NewLine + finalizationNote;
                }
            }

            // Update in repository
            await repository.UpdateAsync(tariffSubmission, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Create response
            var response = MapToDto(tariffSubmission, command.FinalizedBy, command.FinalizationNotes);

            return Result<FinalizeTariffSubmissionResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<FinalizeTariffSubmissionResponse>.Failure($"Failed to finalize tariff submission: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the FinalizeTariffSubmission command
    /// </summary>
    /// <param name="command">Command to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateCommand(FinalizeTariffSubmissionCommand command)
    {
        var errors = new List<string>();

        // Validate ID
        if (command.Id == Guid.Empty)
            errors.Add("Tariff submission ID is required");

        // Validate FinalizedBy
        if (string.IsNullOrWhiteSpace(command.FinalizedBy))
            errors.Add("FinalizedBy is required when finalizing a tariff submission");

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Validates that the submission meets all requirements for finalization
    /// </summary>
    /// <param name="submission">The submission to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateFinalizationRequirements(TariffSubmission submission)
    {
        var errors = new List<string>();

        // Ensure submission was properly submitted
        if (!submission.SubmittedAt.HasValue || string.IsNullOrWhiteSpace(submission.SubmittedBy))
        {
            errors.Add("Submission must have valid submission details before finalization");
        }

        // Check that all required fields are complete
        if (string.IsNullOrWhiteSpace(submission.WTOCountryCode))
            errors.Add("WTO Country Code is required for finalization");

        if (string.IsNullOrWhiteSpace(submission.Currency))
            errors.Add("Currency is required for finalization");

        if (submission.Year <= 0)
            errors.Add("Valid year is required for finalization");

        // Check date range validity
        if (submission.DutiesApplicableFrom >= submission.DutiesApplicableTo)
            errors.Add("Valid duty date range is required for finalization");

        // Check that at least one tariff type is selected
        if (!submission.PreferentialTariffs && !submission.BeneficiaryList && !submission.OtherDutiesAndCharges)
            errors.Add("At least one tariff type must be selected for finalization");

        // Additional business rules for finalization can be added here
        // For example: checking if all required documents are uploaded, etc.

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Maps a TariffSubmission entity to FinalizeTariffSubmissionResponse DTO
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <param name="finalizedBy">User who finalized the submission</param>
    /// <param name="finalizationNotes">Finalization notes</param>
    /// <returns>Mapped DTO</returns>
    private static FinalizeTariffSubmissionResponse MapToDto(TariffSubmission entity, string? finalizedBy, string? finalizationNotes)
    {
        return new FinalizeTariffSubmissionResponse
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy,
            FinalizedAt = entity.ModifiedAt ?? DateTime.UtcNow,
            FinalizedBy = finalizedBy,
            FinalizationNotes = finalizationNotes
        };
    }
}
