using System.Text.RegularExpressions;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;

/// <summary>
/// Command to create a new tariff submission
/// </summary>
public class CreateTariffSubmissionCommand
{
    /// <summary>
    /// WTO Country Code (ISO 3166-1 alpha-3)
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Year of the tariff submission
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// Additional contact emails for the submission
    /// </summary>
    public string? AdditionalContactEmails { get; set; }

    /// <summary>
    /// Origin of the tariff submission
    /// </summary>
    public TariffSubmissionOrigin TariffSubmissionOrigin { get; set; }

    /// <summary>
    /// Original language of the submission
    /// </summary>
    public WTOLanguage OriginalLanguage { get; set; }

    /// <summary>
    /// Currency used in the tariff submission (ISO 4217 code)
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Indicates if preferential tariffs are included
    /// </summary>
    public bool PreferentialTariffs { get; set; }

    /// <summary>
    /// Indicates if beneficiary list is included
    /// </summary>
    public bool BeneficiaryList { get; set; }

    /// <summary>
    /// Indicates if other duties and charges are included
    /// </summary>
    public bool OtherDutiesAndCharges { get; set; }

    /// <summary>
    /// Date from which the duties are applicable
    /// </summary>
    public DateOnly DutiesApplicableFrom { get; set; }

    /// <summary>
    /// Date until which the duties are applicable
    /// </summary>
    public DateOnly DutiesApplicableTo { get; set; }

    /// <summary>
    /// Additional description or notes for the submission
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// User creating the submission
    /// </summary>
    public string? CreatedBy { get; set; }
}

/// <summary>
/// Response for CreateTariffSubmission command
/// </summary>
public class CreateTariffSubmissionResponse
{
    /// <summary>
    /// Created tariff submission's ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// WTO Country Code
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Year of the submission
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// Current status of the submission
    /// </summary>
    public SubmissionStatus Status { get; set; }

    /// <summary>
    /// Date when the submission was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// User who created the submission
    /// </summary>
    public string? CreatedBy { get; set; }
}

/// <summary>
/// Handler for CreateTariffSubmission command
/// </summary>
public class CreateTariffSubmissionHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the CreateTariffSubmissionHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public CreateTariffSubmissionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the CreateTariffSubmission command
    /// </summary>
    /// <param name="command">The command to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the created tariff submission response</returns>
    public async Task<Result<CreateTariffSubmissionResponse>> HandleAsync(CreateTariffSubmissionCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the command
            var validationResult = await ValidateCommandAsync(command, cancellationToken);
            if (!validationResult.IsSuccess)
            {
                return Result<CreateTariffSubmissionResponse>.Failure(validationResult.Errors);
            }

            // Create the tariff submission entity
            var tariffSubmission = new TariffSubmission
            {
                WTOCountryCode = command.WTOCountryCode.Trim().ToUpperInvariant(),
                Year = command.Year,
                Status = SubmissionStatus.Draft,
                AdditionalContactEmails = command.AdditionalContactEmails?.Trim(),
                TariffSubmissionOrigin = command.TariffSubmissionOrigin,
                OriginalLanguage = command.OriginalLanguage,
                Currency = command.Currency.Trim().ToUpperInvariant(),
                PreferentialTariffs = command.PreferentialTariffs,
                BeneficiaryList = command.BeneficiaryList,
                OtherDutiesAndCharges = command.OtherDutiesAndCharges,
                DutiesApplicableFrom = command.DutiesApplicableFrom,
                DutiesApplicableTo = command.DutiesApplicableTo,
                Description = command.Description?.Trim()
            };

            // Set created by if provided
            if (!string.IsNullOrWhiteSpace(command.CreatedBy))
            {
                tariffSubmission.SetCreatedBy(command.CreatedBy);
            }

            // Add to repository
            var repository = _unitOfWork.Repository<TariffSubmission>();
            await repository.AddAsync(tariffSubmission, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Create response
            var response = new CreateTariffSubmissionResponse
            {
                Id = tariffSubmission.Id,
                WTOCountryCode = tariffSubmission.WTOCountryCode,
                Year = tariffSubmission.Year,
                Status = tariffSubmission.Status,
                CreatedAt = tariffSubmission.CreatedAt,
                CreatedBy = tariffSubmission.CreatedBy
            };

            return Result<CreateTariffSubmissionResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<CreateTariffSubmissionResponse>.Failure($"Failed to create tariff submission: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the CreateTariffSubmission command
    /// </summary>
    /// <param name="command">Command to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    private async Task<Result> ValidateCommandAsync(CreateTariffSubmissionCommand command, CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        // Validate WTO Country Code
        if (string.IsNullOrWhiteSpace(command.WTOCountryCode))
            errors.Add("WTO Country Code is required");
        else if (command.WTOCountryCode.Trim().Length != 3)
            errors.Add("WTO Country Code must be exactly 3 characters");
        else if (!Regex.IsMatch(command.WTOCountryCode.Trim(), @"^[A-Z]{3}$", RegexOptions.IgnoreCase))
            errors.Add("WTO Country Code must contain only letters");

        // Validate Year
        var currentYear = DateTime.UtcNow.Year;
        if (command.Year < 1995) // WTO was established in 1995
            errors.Add("Year cannot be before 1995");
        else if (command.Year > currentYear + 5) // Allow up to 5 years in the future
            errors.Add($"Year cannot be more than 5 years in the future (max: {currentYear + 5})");

        // Validate Currency
        if (string.IsNullOrWhiteSpace(command.Currency))
            errors.Add("Currency is required");
        else if (command.Currency.Trim().Length != 3)
            errors.Add("Currency must be a valid 3-character ISO 4217 code");
        else if (!Regex.IsMatch(command.Currency.Trim(), @"^[A-Z]{3}$", RegexOptions.IgnoreCase))
            errors.Add("Currency must contain only letters");

        // Validate date range
        if (command.DutiesApplicableFrom >= command.DutiesApplicableTo)
            errors.Add("Duties applicable from date must be before duties applicable to date");

        // Validate additional contact emails if provided
        if (!string.IsNullOrWhiteSpace(command.AdditionalContactEmails))
        {
            var emails = command.AdditionalContactEmails.Split(',', StringSplitOptions.RemoveEmptyEntries);
            foreach (var email in emails)
            {
                if (!IsValidEmail(email.Trim()))
                {
                    errors.Add($"Invalid email format: {email.Trim()}");
                }
            }
        }

        // Check for duplicate submission (same country and year)
        var repository = _unitOfWork.Repository<TariffSubmission>();
        var existingSubmission = await repository.FirstOrDefaultAsync(
            ts => ts.WTOCountryCode == command.WTOCountryCode.Trim().ToUpperInvariant() && 
                  ts.Year == command.Year && 
                  !ts.IsDeleted,
            cancellationToken);

        if (existingSubmission != null)
            errors.Add($"A tariff submission for {command.WTOCountryCode.ToUpperInvariant()} in {command.Year} already exists");

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Validates email format
    /// </summary>
    /// <param name="email">Email to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    private static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
            return emailRegex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }
}
