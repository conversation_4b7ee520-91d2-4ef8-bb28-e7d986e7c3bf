using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;

/// <summary>
/// Command to accept a tariff submission (change status from Submitted to Accepted)
/// </summary>
public class AcceptTariffSubmissionCommand
{
    /// <summary>
    /// ID of the tariff submission to accept
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// User accepting the tariff submission
    /// </summary>
    public string? AcceptedBy { get; set; }

    /// <summary>
    /// Optional notes about the acceptance
    /// </summary>
    public string? AcceptanceNotes { get; set; }
}

/// <summary>
/// Response for AcceptTariffSubmission command
/// </summary>
public class AcceptTariffSubmissionResponse : TariffSubmissionDto
{
    /// <summary>
    /// Date when the submission was accepted
    /// </summary>
    public DateTime AcceptedAt { get; set; }

    /// <summary>
    /// User who accepted the submission
    /// </summary>
    public string? AcceptedBy { get; set; }

    /// <summary>
    /// Notes about the acceptance
    /// </summary>
    public string? AcceptanceNotes { get; set; }
}

/// <summary>
/// Handler for AcceptTariffSubmission command
/// </summary>
public class AcceptTariffSubmissionHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the AcceptTariffSubmissionHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public AcceptTariffSubmissionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the AcceptTariffSubmission command
    /// </summary>
    /// <param name="command">The command to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the accepted tariff submission response</returns>
    public async Task<Result<AcceptTariffSubmissionResponse>> HandleAsync(AcceptTariffSubmissionCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the command
            var validationResult = ValidateCommand(command);
            if (!validationResult.IsSuccess)
            {
                return Result<AcceptTariffSubmissionResponse>.Failure(validationResult.Errors);
            }

            // Get the existing tariff submission
            var repository = _unitOfWork.Repository<TariffSubmission>();
            var tariffSubmission = await repository.GetByIdAsync(command.Id, cancellationToken);

            if (tariffSubmission == null)
            {
                return Result<AcceptTariffSubmissionResponse>.Failure($"No tariff submission found with ID: {command.Id}");
            }

            if (tariffSubmission.IsDeleted)
            {
                return Result<AcceptTariffSubmissionResponse>.Failure("Cannot accept a deleted tariff submission");
            }

            // Check if submission can be accepted (only Submitted status can be accepted)
            if (tariffSubmission.Status != SubmissionStatus.Submitted)
            {
                return Result<AcceptTariffSubmissionResponse>.Failure($"Cannot accept tariff submission with status: {tariffSubmission.Status}. Only Submitted submissions can be accepted.");
            }

            // Update status
            tariffSubmission.Status = SubmissionStatus.Accepted;
            tariffSubmission.SetModified(command.AcceptedBy);

            // Update description with acceptance notes if provided
            if (!string.IsNullOrWhiteSpace(command.AcceptanceNotes))
            {
                var acceptanceNote = $"[ACCEPTED {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC by {command.AcceptedBy}]: {command.AcceptanceNotes.Trim()}";
                
                if (string.IsNullOrWhiteSpace(tariffSubmission.Description))
                {
                    tariffSubmission.Description = acceptanceNote;
                }
                else
                {
                    tariffSubmission.Description += Environment.NewLine + acceptanceNote;
                }
            }

            // Update in repository
            await repository.UpdateAsync(tariffSubmission, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Create response
            var response = MapToDto(tariffSubmission, command.AcceptedBy, command.AcceptanceNotes);

            return Result<AcceptTariffSubmissionResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<AcceptTariffSubmissionResponse>.Failure($"Failed to accept tariff submission: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the AcceptTariffSubmission command
    /// </summary>
    /// <param name="command">Command to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateCommand(AcceptTariffSubmissionCommand command)
    {
        var errors = new List<string>();

        // Validate ID
        if (command.Id == Guid.Empty)
            errors.Add("Tariff submission ID is required");

        // Validate AcceptedBy
        if (string.IsNullOrWhiteSpace(command.AcceptedBy))
            errors.Add("AcceptedBy is required when accepting a tariff submission");

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Maps a TariffSubmission entity to AcceptTariffSubmissionResponse DTO
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <param name="acceptedBy">User who accepted the submission</param>
    /// <param name="acceptanceNotes">Acceptance notes</param>
    /// <returns>Mapped DTO</returns>
    private static AcceptTariffSubmissionResponse MapToDto(TariffSubmission entity, string? acceptedBy, string? acceptanceNotes)
    {
        return new AcceptTariffSubmissionResponse
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy,
            AcceptedAt = entity.ModifiedAt ?? DateTime.UtcNow,
            AcceptedBy = acceptedBy,
            AcceptanceNotes = acceptanceNotes
        };
    }
}
