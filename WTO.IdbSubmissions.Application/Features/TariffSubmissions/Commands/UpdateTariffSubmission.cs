using System.Text.RegularExpressions;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;

/// <summary>
/// Command to update an existing tariff submission
/// </summary>
public class UpdateTariffSubmissionCommand
{
    /// <summary>
    /// ID of the tariff submission to update
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// WTO Country Code (ISO 3166-1 alpha-3)
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Year of the tariff submission
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// Additional contact emails for the submission
    /// </summary>
    public string? AdditionalContactEmails { get; set; }

    /// <summary>
    /// Origin of the tariff submission
    /// </summary>
    public TariffSubmissionOrigin TariffSubmissionOrigin { get; set; }

    /// <summary>
    /// Original language of the submission
    /// </summary>
    public WTOLanguage OriginalLanguage { get; set; }

    /// <summary>
    /// Currency used in the tariff submission (ISO 4217 code)
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Indicates if preferential tariffs are included
    /// </summary>
    public bool PreferentialTariffs { get; set; }

    /// <summary>
    /// Indicates if beneficiary list is included
    /// </summary>
    public bool BeneficiaryList { get; set; }

    /// <summary>
    /// Indicates if other duties and charges are included
    /// </summary>
    public bool OtherDutiesAndCharges { get; set; }

    /// <summary>
    /// Date from which the duties are applicable
    /// </summary>
    public DateOnly DutiesApplicableFrom { get; set; }

    /// <summary>
    /// Date until which the duties are applicable
    /// </summary>
    public DateOnly DutiesApplicableTo { get; set; }

    /// <summary>
    /// Additional description or notes for the submission
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// User updating the submission
    /// </summary>
    public string? ModifiedBy { get; set; }
}

/// <summary>
/// Response for UpdateTariffSubmission command
/// </summary>
public class UpdateTariffSubmissionResponse : TariffSubmissionDto
{
    // Inherits all properties from TariffSubmissionDto
}

/// <summary>
/// Handler for UpdateTariffSubmission command
/// </summary>
public class UpdateTariffSubmissionHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the UpdateTariffSubmissionHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public UpdateTariffSubmissionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the UpdateTariffSubmission command
    /// </summary>
    /// <param name="command">The command to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the updated tariff submission response</returns>
    public async Task<Result<UpdateTariffSubmissionResponse>> HandleAsync(UpdateTariffSubmissionCommand command, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the command
            var validationResult = await ValidateCommandAsync(command, cancellationToken);
            if (!validationResult.IsSuccess)
            {
                return Result<UpdateTariffSubmissionResponse>.Failure(validationResult.Errors);
            }

            // Get the existing tariff submission
            var repository = _unitOfWork.Repository<TariffSubmission>();
            var tariffSubmission = await repository.GetByIdAsync(command.Id, cancellationToken);

            if (tariffSubmission == null)
            {
                return Result<UpdateTariffSubmissionResponse>.Failure($"No tariff submission found with ID: {command.Id}");
            }

            if (tariffSubmission.IsDeleted)
            {
                return Result<UpdateTariffSubmissionResponse>.Failure("Cannot update a deleted tariff submission");
            }

            // Check if submission can be updated (only Draft and Submitted status can be updated)
            if (tariffSubmission.Status == SubmissionStatus.Accepted || tariffSubmission.Status == SubmissionStatus.Finalized)
            {
                return Result<UpdateTariffSubmissionResponse>.Failure($"Cannot update tariff submission with status: {tariffSubmission.Status}");
            }

            // Update the entity properties
            tariffSubmission.WTOCountryCode = command.WTOCountryCode.Trim().ToUpperInvariant();
            tariffSubmission.Year = command.Year;
            tariffSubmission.AdditionalContactEmails = command.AdditionalContactEmails?.Trim();
            tariffSubmission.TariffSubmissionOrigin = command.TariffSubmissionOrigin;
            tariffSubmission.OriginalLanguage = command.OriginalLanguage;
            tariffSubmission.Currency = command.Currency.Trim().ToUpperInvariant();
            tariffSubmission.PreferentialTariffs = command.PreferentialTariffs;
            tariffSubmission.BeneficiaryList = command.BeneficiaryList;
            tariffSubmission.OtherDutiesAndCharges = command.OtherDutiesAndCharges;
            tariffSubmission.DutiesApplicableFrom = command.DutiesApplicableFrom;
            tariffSubmission.DutiesApplicableTo = command.DutiesApplicableTo;
            tariffSubmission.Description = command.Description?.Trim();

            // Set modified by and timestamp
            tariffSubmission.SetModified(command.ModifiedBy);

            // Update in repository
            await repository.UpdateAsync(tariffSubmission, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Create response
            var response = MapToDto(tariffSubmission);

            return Result<UpdateTariffSubmissionResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<UpdateTariffSubmissionResponse>.Failure($"Failed to update tariff submission: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the UpdateTariffSubmission command
    /// </summary>
    /// <param name="command">Command to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation result</returns>
    private async Task<Result> ValidateCommandAsync(UpdateTariffSubmissionCommand command, CancellationToken cancellationToken)
    {
        var errors = new List<string>();

        // Validate ID
        if (command.Id == Guid.Empty)
            errors.Add("Tariff submission ID is required");

        // Validate WTO Country Code
        if (string.IsNullOrWhiteSpace(command.WTOCountryCode))
            errors.Add("WTO Country Code is required");
        else if (command.WTOCountryCode.Trim().Length != 3)
            errors.Add("WTO Country Code must be exactly 3 characters");
        else if (!Regex.IsMatch(command.WTOCountryCode.Trim(), @"^[A-Z]{3}$", RegexOptions.IgnoreCase))
            errors.Add("WTO Country Code must contain only letters");

        // Validate Year
        var currentYear = DateTime.UtcNow.Year;
        if (command.Year < 1995) // WTO was established in 1995
            errors.Add("Year cannot be before 1995");
        else if (command.Year > currentYear + 5) // Allow up to 5 years in the future
            errors.Add($"Year cannot be more than 5 years in the future (max: {currentYear + 5})");

        // Validate Currency
        if (string.IsNullOrWhiteSpace(command.Currency))
            errors.Add("Currency is required");
        else if (command.Currency.Trim().Length != 3)
            errors.Add("Currency must be a valid 3-character ISO 4217 code");
        else if (!Regex.IsMatch(command.Currency.Trim(), @"^[A-Z]{3}$", RegexOptions.IgnoreCase))
            errors.Add("Currency must contain only letters");

        // Validate date range
        if (command.DutiesApplicableFrom >= command.DutiesApplicableTo)
            errors.Add("Duties applicable from date must be before duties applicable to date");

        // Validate additional contact emails if provided
        if (!string.IsNullOrWhiteSpace(command.AdditionalContactEmails))
        {
            var emails = command.AdditionalContactEmails.Split(',', StringSplitOptions.RemoveEmptyEntries);
            foreach (var email in emails)
            {
                if (!IsValidEmail(email.Trim()))
                {
                    errors.Add($"Invalid email format: {email.Trim()}");
                }
            }
        }

        // Check for duplicate submission (same country and year, but different ID)
        if (command.Id != Guid.Empty)
        {
            var repository = _unitOfWork.Repository<TariffSubmission>();
            var existingSubmission = await repository.FirstOrDefaultAsync(
                ts => ts.WTOCountryCode == command.WTOCountryCode.Trim().ToUpperInvariant() && 
                      ts.Year == command.Year && 
                      ts.Id != command.Id &&
                      !ts.IsDeleted,
                cancellationToken);

            if (existingSubmission != null)
                errors.Add($"Another tariff submission for {command.WTOCountryCode.ToUpperInvariant()} in {command.Year} already exists");
        }

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Maps a TariffSubmission entity to UpdateTariffSubmissionResponse DTO
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <returns>Mapped DTO</returns>
    private static UpdateTariffSubmissionResponse MapToDto(TariffSubmission entity)
    {
        return new UpdateTariffSubmissionResponse
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy
        };
    }

    /// <summary>
    /// Validates email format
    /// </summary>
    /// <param name="email">Email to validate</param>
    /// <returns>True if valid, false otherwise</returns>
    private static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
            return emailRegex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }
}
