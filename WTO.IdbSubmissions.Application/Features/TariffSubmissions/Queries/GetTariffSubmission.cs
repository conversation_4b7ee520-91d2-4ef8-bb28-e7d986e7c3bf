using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;

/// <summary>
/// Query to get a tariff submission by ID
/// </summary>
public class GetTariffSubmissionQuery
{
    /// <summary>
    /// Tariff submission ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Whether to include soft-deleted submissions
    /// </summary>
    public bool IncludeDeleted { get; set; } = false;
}

/// <summary>
/// Response for GetTariffSubmission query
/// </summary>
public class GetTariffSubmissionResponse : TariffSubmissionDto
{
    // Inherits all properties from TariffSubmissionDto
}

/// <summary>
/// Handler for GetTariffSubmission query
/// </summary>
public class GetTariffSubmissionHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the GetTariffSubmissionHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public GetTariffSubmissionHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the GetTariffSubmission query
    /// </summary>
    /// <param name="query">The query to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the tariff submission response</returns>
    public async Task<Result<GetTariffSubmissionResponse>> HandleAsync(GetTariffSubmissionQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the query
            if (query.Id == Guid.Empty)
            {
                return Result<GetTariffSubmissionResponse>.Failure("Tariff submission ID is required");
            }

            // Get the tariff submission from repository
            var repository = _unitOfWork.Repository<TariffSubmission>();
            var tariffSubmission = await repository.GetByIdAsync(query.Id, cancellationToken);

            // Check if submission exists
            if (tariffSubmission == null)
            {
                return Result<GetTariffSubmissionResponse>.Failure($"No tariff submission found with ID: {query.Id}");
            }

            // Check if submission is deleted and we're not including deleted items
            if (tariffSubmission.IsDeleted && !query.IncludeDeleted)
            {
                return Result<GetTariffSubmissionResponse>.Failure($"Tariff submission with ID {query.Id} has been deleted");
            }

            // Map to response DTO
            var response = MapToDto(tariffSubmission);

            return Result<GetTariffSubmissionResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<GetTariffSubmissionResponse>.Failure($"Failed to retrieve tariff submission: {ex.Message}");
        }
    }

    /// <summary>
    /// Maps a TariffSubmission entity to GetTariffSubmissionResponse DTO
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <returns>Mapped DTO</returns>
    private static GetTariffSubmissionResponse MapToDto(TariffSubmission entity)
    {
        return new GetTariffSubmissionResponse
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy
        };
    }
}
