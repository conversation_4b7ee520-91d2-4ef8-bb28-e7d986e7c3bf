using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;

/// <summary>
/// Query to get tariff submissions by status
/// </summary>
public class GetTariffSubmissionsByStatusQuery
{
    /// <summary>
    /// Status to filter by
    /// </summary>
    public SubmissionStatus Status { get; set; }

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Optional year filter
    /// </summary>
    public int? Year { get; set; }

    /// <summary>
    /// Optional country code filter
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// Whether to include soft-deleted submissions
    /// </summary>
    public bool IncludeDeleted { get; set; } = false;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "ModifiedAt";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}

/// <summary>
/// Response for GetTariffSubmissionsByStatus query
/// </summary>
public class GetTariffSubmissionsByStatusResponse
{
    /// <summary>
    /// Status being queried
    /// </summary>
    public SubmissionStatus Status { get; set; }

    /// <summary>
    /// List of tariff submissions with the specified status
    /// </summary>
    public IEnumerable<TariffSubmissionDto> Submissions { get; set; } = new List<TariffSubmissionDto>();

    /// <summary>
    /// Total number of submissions with this status (before pagination)
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }

    /// <summary>
    /// Summary by country for this status
    /// </summary>
    public Dictionary<string, int> CountrySummary { get; set; } = new();

    /// <summary>
    /// Summary by year for this status
    /// </summary>
    public Dictionary<int, int> YearSummary { get; set; } = new();
}

/// <summary>
/// Handler for GetTariffSubmissionsByStatus query
/// </summary>
public class GetTariffSubmissionsByStatusHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the GetTariffSubmissionsByStatusHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public GetTariffSubmissionsByStatusHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the GetTariffSubmissionsByStatus query
    /// </summary>
    /// <param name="query">The query to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the status-filtered tariff submissions response</returns>
    public async Task<Result<GetTariffSubmissionsByStatusResponse>> HandleAsync(GetTariffSubmissionsByStatusQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the query
            var validationResult = ValidateQuery(query);
            if (!validationResult.IsSuccess)
            {
                return Result<GetTariffSubmissionsByStatusResponse>.Failure(validationResult.Errors);
            }

            var repository = _unitOfWork.Repository<TariffSubmission>();

            // Build filter expression
            var baseFilter = BuildBaseFilter(query);

            // Get all submissions for statistics
            var allSubmissions = await repository.GetAllAsync(baseFilter, cancellationToken);

            // Apply additional filters
            var filteredSubmissions = allSubmissions.AsQueryable();

            if (query.Year.HasValue)
            {
                filteredSubmissions = filteredSubmissions.Where(ts => ts.Year == query.Year.Value);
            }

            if (!string.IsNullOrWhiteSpace(query.CountryCode))
            {
                var countryCode = query.CountryCode.Trim().ToUpperInvariant();
                filteredSubmissions = filteredSubmissions.Where(ts => ts.WTOCountryCode == countryCode);
            }

            var finalSubmissions = filteredSubmissions.ToList();

            // Calculate pagination
            var totalCount = finalSubmissions.Count;
            var totalPages = (int)Math.Ceiling((double)totalCount / query.PageSize);
            var skip = (query.PageNumber - 1) * query.PageSize;

            // Apply sorting
            var sortedSubmissions = ApplySorting(finalSubmissions, query.SortBy, query.SortDirection);

            // Apply pagination
            var pagedSubmissions = sortedSubmissions.Skip(skip).Take(query.PageSize).ToList();

            // Calculate summaries
            var countrySummary = allSubmissions
                .GroupBy(ts => ts.WTOCountryCode)
                .ToDictionary(g => g.Key, g => g.Count());

            var yearSummary = allSubmissions
                .GroupBy(ts => ts.Year)
                .ToDictionary(g => g.Key, g => g.Count());

            // Map to DTOs
            var submissionDtos = pagedSubmissions.Select(MapToDto).ToList();

            // Create response
            var response = new GetTariffSubmissionsByStatusResponse
            {
                Status = query.Status,
                Submissions = submissionDtos,
                TotalCount = totalCount,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize,
                TotalPages = totalPages,
                HasPreviousPage = query.PageNumber > 1,
                HasNextPage = query.PageNumber < totalPages,
                CountrySummary = countrySummary,
                YearSummary = yearSummary
            };

            return Result<GetTariffSubmissionsByStatusResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<GetTariffSubmissionsByStatusResponse>.Failure($"Failed to retrieve tariff submissions by status: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the GetTariffSubmissionsByStatus query
    /// </summary>
    /// <param name="query">Query to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateQuery(GetTariffSubmissionsByStatusQuery query)
    {
        var errors = new List<string>();

        // Validate pagination
        if (query.PageNumber < 1)
            errors.Add("Page number must be greater than 0");

        if (query.PageSize < 1)
            errors.Add("Page size must be greater than 0");

        if (query.PageSize > 100)
            errors.Add("Page size cannot exceed 100");

        // Validate sort direction
        if (!string.IsNullOrEmpty(query.SortDirection) && 
            !query.SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase) &&
            !query.SortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("Sort direction must be 'asc' or 'desc'");
        }

        // Validate country code if provided
        if (!string.IsNullOrWhiteSpace(query.CountryCode) && query.CountryCode.Trim().Length != 3)
        {
            errors.Add("Country code must be exactly 3 characters");
        }

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Builds the base filter expression
    /// </summary>
    /// <param name="query">Query parameters</param>
    /// <returns>Filter expression</returns>
    private static System.Linq.Expressions.Expression<Func<TariffSubmission, bool>> BuildBaseFilter(GetTariffSubmissionsByStatusQuery query)
    {
        if (query.IncludeDeleted)
        {
            return ts => ts.Status == query.Status;
        }
        else
        {
            return ts => ts.Status == query.Status && !ts.IsDeleted;
        }
    }

    /// <summary>
    /// Applies sorting to the submissions list
    /// </summary>
    /// <param name="submissions">Submissions to sort</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <returns>Sorted submissions</returns>
    private static IEnumerable<TariffSubmission> ApplySorting(IEnumerable<TariffSubmission> submissions, string sortBy, string sortDirection)
    {
        var isDescending = sortDirection.ToLowerInvariant() == "desc";

        return sortBy?.ToLowerInvariant() switch
        {
            "countrycode" => isDescending 
                ? submissions.OrderByDescending(ts => ts.WTOCountryCode)
                : submissions.OrderBy(ts => ts.WTOCountryCode),
            "year" => isDescending 
                ? submissions.OrderByDescending(ts => ts.Year)
                : submissions.OrderBy(ts => ts.Year),
            "submittedat" => isDescending 
                ? submissions.OrderByDescending(ts => ts.SubmittedAt ?? DateTime.MinValue)
                : submissions.OrderBy(ts => ts.SubmittedAt ?? DateTime.MinValue),
            "createdat" => isDescending 
                ? submissions.OrderByDescending(ts => ts.CreatedAt)
                : submissions.OrderBy(ts => ts.CreatedAt),
            _ => isDescending 
                ? submissions.OrderByDescending(ts => ts.ModifiedAt ?? ts.CreatedAt)
                : submissions.OrderBy(ts => ts.ModifiedAt ?? ts.CreatedAt)
        };
    }

    /// <summary>
    /// Maps a TariffSubmission entity to TariffSubmissionDto
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <returns>Mapped DTO</returns>
    private static TariffSubmissionDto MapToDto(TariffSubmission entity)
    {
        return new TariffSubmissionDto
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy
        };
    }
}
