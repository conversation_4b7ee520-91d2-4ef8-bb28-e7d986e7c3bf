using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;

/// <summary>
/// Query to get tariff submissions for a specific year
/// </summary>
public class GetTariffSubmissionsByYearQuery
{
    /// <summary>
    /// Year to filter by
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// Optional country code filter
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// Optional status filter
    /// </summary>
    public SubmissionStatus? Status { get; set; }

    /// <summary>
    /// Whether to include soft-deleted submissions
    /// </summary>
    public bool IncludeDeleted { get; set; } = false;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "CountryCode";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "asc";
}

/// <summary>
/// Response for GetTariffSubmissionsByYear query
/// </summary>
public class GetTariffSubmissionsByYearResponse
{
    /// <summary>
    /// Year being queried
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// List of tariff submissions for the year
    /// </summary>
    public IEnumerable<TariffSubmissionDto> Submissions { get; set; } = new List<TariffSubmissionDto>();

    /// <summary>
    /// Total number of submissions found
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Summary statistics by status
    /// </summary>
    public Dictionary<SubmissionStatus, int> StatusSummary { get; set; } = new();

    /// <summary>
    /// Summary by country
    /// </summary>
    public Dictionary<string, int> CountrySummary { get; set; } = new();

    /// <summary>
    /// Summary by origin
    /// </summary>
    public Dictionary<TariffSubmissionOrigin, int> OriginSummary { get; set; } = new();
}

/// <summary>
/// Handler for GetTariffSubmissionsByYear query
/// </summary>
public class GetTariffSubmissionsByYearHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the GetTariffSubmissionsByYearHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public GetTariffSubmissionsByYearHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the GetTariffSubmissionsByYear query
    /// </summary>
    /// <param name="query">The query to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the year's tariff submissions response</returns>
    public async Task<Result<GetTariffSubmissionsByYearResponse>> HandleAsync(GetTariffSubmissionsByYearQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the query
            var validationResult = ValidateQuery(query);
            if (!validationResult.IsSuccess)
            {
                return Result<GetTariffSubmissionsByYearResponse>.Failure(validationResult.Errors);
            }

            var repository = _unitOfWork.Repository<TariffSubmission>();

            // Build base filter for year
            var baseFilter = BuildBaseFilter(query.Year, query.IncludeDeleted);

            // Get all submissions for the year (for statistics)
            var allSubmissions = await repository.GetAllAsync(baseFilter, cancellationToken);

            // Apply additional filters for the main result set
            var filteredSubmissions = allSubmissions.AsQueryable();

            if (!string.IsNullOrWhiteSpace(query.CountryCode))
            {
                var countryCode = query.CountryCode.Trim().ToUpperInvariant();
                filteredSubmissions = filteredSubmissions.Where(ts => ts.WTOCountryCode == countryCode);
            }

            if (query.Status.HasValue)
            {
                filteredSubmissions = filteredSubmissions.Where(ts => ts.Status == query.Status.Value);
            }

            // Apply sorting
            var sortedSubmissions = ApplySorting(filteredSubmissions, query.SortBy, query.SortDirection);
            var finalSubmissions = sortedSubmissions.ToList();

            // Calculate statistics
            var statusSummary = allSubmissions
                .GroupBy(ts => ts.Status)
                .ToDictionary(g => g.Key, g => g.Count());

            var countrySummary = allSubmissions
                .GroupBy(ts => ts.WTOCountryCode)
                .ToDictionary(g => g.Key, g => g.Count());

            var originSummary = allSubmissions
                .GroupBy(ts => ts.TariffSubmissionOrigin)
                .ToDictionary(g => g.Key, g => g.Count());

            // Map to DTOs
            var submissionDtos = finalSubmissions.Select(MapToDto).ToList();

            // Create response
            var response = new GetTariffSubmissionsByYearResponse
            {
                Year = query.Year,
                Submissions = submissionDtos,
                TotalCount = finalSubmissions.Count,
                StatusSummary = statusSummary,
                CountrySummary = countrySummary,
                OriginSummary = originSummary
            };

            return Result<GetTariffSubmissionsByYearResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<GetTariffSubmissionsByYearResponse>.Failure($"Failed to retrieve tariff submissions for year: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the GetTariffSubmissionsByYear query
    /// </summary>
    /// <param name="query">Query to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateQuery(GetTariffSubmissionsByYearQuery query)
    {
        var errors = new List<string>();

        // Validate year
        if (query.Year < 1995) // WTO was established in 1995
            errors.Add("Year cannot be before 1995");
        else if (query.Year > DateTime.UtcNow.Year + 5) // Allow up to 5 years in the future
            errors.Add($"Year cannot be more than 5 years in the future");

        // Validate country code if provided
        if (!string.IsNullOrWhiteSpace(query.CountryCode) && query.CountryCode.Trim().Length != 3)
        {
            errors.Add("Country code must be exactly 3 characters");
        }

        // Validate sort direction
        if (!string.IsNullOrEmpty(query.SortDirection) && 
            !query.SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase) &&
            !query.SortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("Sort direction must be 'asc' or 'desc'");
        }

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Builds the base filter expression for the year
    /// </summary>
    /// <param name="year">Year to filter by</param>
    /// <param name="includeDeleted">Whether to include deleted submissions</param>
    /// <returns>Filter expression</returns>
    private static System.Linq.Expressions.Expression<Func<TariffSubmission, bool>> BuildBaseFilter(int year, bool includeDeleted)
    {
        if (includeDeleted)
        {
            return ts => ts.Year == year;
        }
        else
        {
            return ts => ts.Year == year && !ts.IsDeleted;
        }
    }

    /// <summary>
    /// Applies sorting to the submissions list
    /// </summary>
    /// <param name="submissions">Submissions to sort</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <returns>Sorted submissions</returns>
    private static IEnumerable<TariffSubmission> ApplySorting(IEnumerable<TariffSubmission> submissions, string sortBy, string sortDirection)
    {
        var isDescending = sortDirection.ToLowerInvariant() == "desc";

        return sortBy?.ToLowerInvariant() switch
        {
            "status" => isDescending 
                ? submissions.OrderByDescending(ts => ts.Status)
                : submissions.OrderBy(ts => ts.Status),
            "submittedat" => isDescending 
                ? submissions.OrderByDescending(ts => ts.SubmittedAt ?? DateTime.MinValue)
                : submissions.OrderBy(ts => ts.SubmittedAt ?? DateTime.MinValue),
            "createdat" => isDescending 
                ? submissions.OrderByDescending(ts => ts.CreatedAt)
                : submissions.OrderBy(ts => ts.CreatedAt),
            "modifiedat" => isDescending 
                ? submissions.OrderByDescending(ts => ts.ModifiedAt ?? ts.CreatedAt)
                : submissions.OrderBy(ts => ts.ModifiedAt ?? ts.CreatedAt),
            _ => isDescending 
                ? submissions.OrderByDescending(ts => ts.WTOCountryCode)
                : submissions.OrderBy(ts => ts.WTOCountryCode)
        };
    }

    /// <summary>
    /// Maps a TariffSubmission entity to TariffSubmissionDto
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <returns>Mapped DTO</returns>
    private static TariffSubmissionDto MapToDto(TariffSubmission entity)
    {
        return new TariffSubmissionDto
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy
        };
    }
}
