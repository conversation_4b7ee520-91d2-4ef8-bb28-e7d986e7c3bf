using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;

/// <summary>
/// Query to get tariff submissions for a specific country
/// </summary>
public class GetTariffSubmissionsByCountryQuery
{
    /// <summary>
    /// WTO Country Code (ISO 3166-1 alpha-3)
    /// </summary>
    public string CountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Optional year filter
    /// </summary>
    public int? Year { get; set; }

    /// <summary>
    /// Optional status filter
    /// </summary>
    public SubmissionStatus? Status { get; set; }

    /// <summary>
    /// Whether to include soft-deleted submissions
    /// </summary>
    public bool IncludeDeleted { get; set; } = false;

    /// <summary>
    /// Sort direction (asc/desc) - defaults to desc by year
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}

/// <summary>
/// Response for GetTariffSubmissionsByCountry query
/// </summary>
public class GetTariffSubmissionsByCountryResponse
{
    /// <summary>
    /// Country code for the submissions
    /// </summary>
    public string CountryCode { get; set; } = string.Empty;

    /// <summary>
    /// List of tariff submissions for the country
    /// </summary>
    public IEnumerable<TariffSubmissionDto> Submissions { get; set; } = new List<TariffSubmissionDto>();

    /// <summary>
    /// Total number of submissions found
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Summary statistics by status
    /// </summary>
    public Dictionary<SubmissionStatus, int> StatusSummary { get; set; } = new();

    /// <summary>
    /// Years covered by the submissions
    /// </summary>
    public IEnumerable<int> YearsCovered { get; set; } = new List<int>();
}

/// <summary>
/// Handler for GetTariffSubmissionsByCountry query
/// </summary>
public class GetTariffSubmissionsByCountryHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the GetTariffSubmissionsByCountryHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public GetTariffSubmissionsByCountryHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the GetTariffSubmissionsByCountry query
    /// </summary>
    /// <param name="query">The query to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the country's tariff submissions response</returns>
    public async Task<Result<GetTariffSubmissionsByCountryResponse>> HandleAsync(GetTariffSubmissionsByCountryQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the query
            var validationResult = ValidateQuery(query);
            if (!validationResult.IsSuccess)
            {
                return Result<GetTariffSubmissionsByCountryResponse>.Failure(validationResult.Errors);
            }

            var countryCode = query.CountryCode.Trim().ToUpperInvariant();
            var repository = _unitOfWork.Repository<TariffSubmission>();

            // Build base filter for country
            var baseFilter = BuildBaseFilter(countryCode, query.IncludeDeleted);

            // Get all submissions for the country (for statistics)
            var allSubmissions = await repository.GetAllAsync(baseFilter, cancellationToken);

            // Apply additional filters for the main result set
            var filteredSubmissions = allSubmissions.AsQueryable();

            if (query.Year.HasValue)
            {
                filteredSubmissions = filteredSubmissions.Where(ts => ts.Year == query.Year.Value);
            }

            if (query.Status.HasValue)
            {
                filteredSubmissions = filteredSubmissions.Where(ts => ts.Status == query.Status.Value);
            }

            // Sort the results
            if (query.SortDirection.ToLowerInvariant() == "asc")
            {
                filteredSubmissions = filteredSubmissions.OrderBy(ts => ts.Year).ThenBy(ts => ts.CreatedAt);
            }
            else
            {
                filteredSubmissions = filteredSubmissions.OrderByDescending(ts => ts.Year).ThenByDescending(ts => ts.CreatedAt);
            }

            var finalSubmissions = filteredSubmissions.ToList();

            // Calculate statistics
            var statusSummary = allSubmissions
                .GroupBy(ts => ts.Status)
                .ToDictionary(g => g.Key, g => g.Count());

            var yearsCovered = allSubmissions
                .Select(ts => ts.Year)
                .Distinct()
                .OrderByDescending(y => y)
                .ToList();

            // Map to DTOs
            var submissionDtos = finalSubmissions.Select(MapToDto).ToList();

            // Create response
            var response = new GetTariffSubmissionsByCountryResponse
            {
                CountryCode = countryCode,
                Submissions = submissionDtos,
                TotalCount = finalSubmissions.Count,
                StatusSummary = statusSummary,
                YearsCovered = yearsCovered
            };

            return Result<GetTariffSubmissionsByCountryResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<GetTariffSubmissionsByCountryResponse>.Failure($"Failed to retrieve tariff submissions for country: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the GetTariffSubmissionsByCountry query
    /// </summary>
    /// <param name="query">Query to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateQuery(GetTariffSubmissionsByCountryQuery query)
    {
        var errors = new List<string>();

        // Validate country code
        if (string.IsNullOrWhiteSpace(query.CountryCode))
            errors.Add("Country code is required");
        else if (query.CountryCode.Trim().Length != 3)
            errors.Add("Country code must be exactly 3 characters");

        // Validate sort direction
        if (!string.IsNullOrEmpty(query.SortDirection) && 
            !query.SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase) &&
            !query.SortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("Sort direction must be 'asc' or 'desc'");
        }

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Builds the base filter expression for the country
    /// </summary>
    /// <param name="countryCode">Country code to filter by</param>
    /// <param name="includeDeleted">Whether to include deleted submissions</param>
    /// <returns>Filter expression</returns>
    private static System.Linq.Expressions.Expression<Func<TariffSubmission, bool>> BuildBaseFilter(string countryCode, bool includeDeleted)
    {
        if (includeDeleted)
        {
            return ts => ts.WTOCountryCode == countryCode;
        }
        else
        {
            return ts => ts.WTOCountryCode == countryCode && !ts.IsDeleted;
        }
    }

    /// <summary>
    /// Maps a TariffSubmission entity to TariffSubmissionDto
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <returns>Mapped DTO</returns>
    private static TariffSubmissionDto MapToDto(TariffSubmission entity)
    {
        return new TariffSubmissionDto
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy
        };
    }
}
