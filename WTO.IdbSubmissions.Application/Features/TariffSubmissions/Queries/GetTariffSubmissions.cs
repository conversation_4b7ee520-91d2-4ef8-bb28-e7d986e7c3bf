using System.Linq.Expressions;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Domain.Entities;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;

/// <summary>
/// Query to get a paginated list of tariff submissions with filtering options
/// </summary>
public class GetTariffSubmissionsQuery
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Filter by WTO Country Code
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// Filter by year
    /// </summary>
    public int? Year { get; set; }

    /// <summary>
    /// Filter by status
    /// </summary>
    public SubmissionStatus? Status { get; set; }

    /// <summary>
    /// Filter by submission origin
    /// </summary>
    public TariffSubmissionOrigin? Origin { get; set; }

    /// <summary>
    /// Filter by original language
    /// </summary>
    public WTOLanguage? Language { get; set; }

    /// <summary>
    /// Search term for description or country code
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Whether to include soft-deleted submissions
    /// </summary>
    public bool IncludeDeleted { get; set; } = false;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "CreatedAt";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}

/// <summary>
/// Response for GetTariffSubmissions query
/// </summary>
public class GetTariffSubmissionsResponse
{
    /// <summary>
    /// List of tariff submissions
    /// </summary>
    public IEnumerable<TariffSubmissionDto> Items { get; set; } = new List<TariffSubmissionDto>();

    /// <summary>
    /// Total number of items (before pagination)
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNumber { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage { get; set; }

    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage { get; set; }
}

/// <summary>
/// Handler for GetTariffSubmissions query
/// </summary>
public class GetTariffSubmissionsHandler
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the GetTariffSubmissionsHandler class
    /// </summary>
    /// <param name="unitOfWork">Unit of work instance</param>
    public GetTariffSubmissionsHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <summary>
    /// Handles the GetTariffSubmissions query
    /// </summary>
    /// <param name="query">The query to handle</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result containing the paginated tariff submissions response</returns>
    public async Task<Result<GetTariffSubmissionsResponse>> HandleAsync(GetTariffSubmissionsQuery query, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the query
            var validationResult = ValidateQuery(query);
            if (!validationResult.IsSuccess)
            {
                return Result<GetTariffSubmissionsResponse>.Failure(validationResult.Errors);
            }

            // Build filter expression
            var filterExpression = BuildFilterExpression(query);

            // Get repository
            var repository = _unitOfWork.Repository<TariffSubmission>();

            // Get total count
            var totalCount = await repository.CountAsync(filterExpression, cancellationToken);

            // Calculate pagination
            var totalPages = (int)Math.Ceiling((double)totalCount / query.PageSize);
            var skip = (query.PageNumber - 1) * query.PageSize;

            // Get paginated data
            var submissions = await repository.GetPagedAsync(
                filterExpression,
                skip,
                query.PageSize,
                GetSortExpression(query.SortBy),
                query.SortDirection.ToLowerInvariant() == "desc",
                cancellationToken);

            // Map to DTOs
            var submissionDtos = submissions.Select(MapToDto).ToList();

            // Create response
            var response = new GetTariffSubmissionsResponse
            {
                Items = submissionDtos,
                TotalCount = totalCount,
                PageNumber = query.PageNumber,
                PageSize = query.PageSize,
                TotalPages = totalPages,
                HasPreviousPage = query.PageNumber > 1,
                HasNextPage = query.PageNumber < totalPages
            };

            return Result<GetTariffSubmissionsResponse>.Success(response);
        }
        catch (Exception ex)
        {
            return Result<GetTariffSubmissionsResponse>.Failure($"Failed to retrieve tariff submissions: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates the GetTariffSubmissions query
    /// </summary>
    /// <param name="query">Query to validate</param>
    /// <returns>Validation result</returns>
    private static Result ValidateQuery(GetTariffSubmissionsQuery query)
    {
        var errors = new List<string>();

        // Validate pagination
        if (query.PageNumber < 1)
            errors.Add("Page number must be greater than 0");

        if (query.PageSize < 1)
            errors.Add("Page size must be greater than 0");

        if (query.PageSize > 100)
            errors.Add("Page size cannot exceed 100");

        // Validate sort direction
        if (!string.IsNullOrEmpty(query.SortDirection) && 
            !query.SortDirection.Equals("asc", StringComparison.OrdinalIgnoreCase) &&
            !query.SortDirection.Equals("desc", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("Sort direction must be 'asc' or 'desc'");
        }

        return errors.Any() ? Result.Failure(errors) : Result.Success();
    }

    /// <summary>
    /// Builds the filter expression based on query parameters
    /// </summary>
    /// <param name="query">The query parameters</param>
    /// <returns>Filter expression</returns>
    private static Expression<Func<TariffSubmission, bool>> BuildFilterExpression(GetTariffSubmissionsQuery query)
    {
        Expression<Func<TariffSubmission, bool>> expression = ts => true;

        // Filter by deleted status
        if (!query.IncludeDeleted)
        {
            expression = CombineExpressions(expression, ts => !ts.IsDeleted);
        }

        // Filter by country code
        if (!string.IsNullOrWhiteSpace(query.CountryCode))
        {
            var countryCode = query.CountryCode.Trim().ToUpperInvariant();
            expression = CombineExpressions(expression, ts => ts.WTOCountryCode == countryCode);
        }

        // Filter by year
        if (query.Year.HasValue)
        {
            expression = CombineExpressions(expression, ts => ts.Year == query.Year.Value);
        }

        // Filter by status
        if (query.Status.HasValue)
        {
            expression = CombineExpressions(expression, ts => ts.Status == query.Status.Value);
        }

        // Filter by origin
        if (query.Origin.HasValue)
        {
            expression = CombineExpressions(expression, ts => ts.TariffSubmissionOrigin == query.Origin.Value);
        }

        // Filter by language
        if (query.Language.HasValue)
        {
            expression = CombineExpressions(expression, ts => ts.OriginalLanguage == query.Language.Value);
        }

        // Filter by search term
        if (!string.IsNullOrWhiteSpace(query.SearchTerm))
        {
            var searchTerm = query.SearchTerm.Trim().ToLowerInvariant();
            expression = CombineExpressions(expression, ts => 
                ts.WTOCountryCode.ToLower().Contains(searchTerm) ||
                (ts.Description != null && ts.Description.ToLower().Contains(searchTerm)));
        }

        return expression;
    }

    /// <summary>
    /// Combines two expressions with AND logic
    /// </summary>
    /// <param name="expr1">First expression</param>
    /// <param name="expr2">Second expression</param>
    /// <returns>Combined expression</returns>
    private static Expression<Func<TariffSubmission, bool>> CombineExpressions(
        Expression<Func<TariffSubmission, bool>> expr1,
        Expression<Func<TariffSubmission, bool>> expr2)
    {
        var parameter = Expression.Parameter(typeof(TariffSubmission));
        var body = Expression.AndAlso(
            Expression.Invoke(expr1, parameter),
            Expression.Invoke(expr2, parameter));
        return Expression.Lambda<Func<TariffSubmission, bool>>(body, parameter);
    }

    /// <summary>
    /// Gets the sort expression based on the sort field name
    /// </summary>
    /// <param name="sortBy">Sort field name</param>
    /// <returns>Sort expression</returns>
    private static Expression<Func<TariffSubmission, object>> GetSortExpression(string sortBy)
    {
        return sortBy?.ToLowerInvariant() switch
        {
            "countrycode" => ts => ts.WTOCountryCode,
            "year" => ts => ts.Year,
            "status" => ts => ts.Status,
            "submittedat" => ts => ts.SubmittedAt ?? DateTime.MinValue,
            "modifiedat" => ts => ts.ModifiedAt ?? DateTime.MinValue,
            _ => ts => ts.CreatedAt
        };
    }

    /// <summary>
    /// Maps a TariffSubmission entity to TariffSubmissionDto
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <returns>Mapped DTO</returns>
    private static TariffSubmissionDto MapToDto(TariffSubmission entity)
    {
        return new TariffSubmissionDto
        {
            Id = entity.Id,
            WTOCountryCode = entity.WTOCountryCode,
            Year = entity.Year,
            Status = entity.Status,
            SubmittedAt = entity.SubmittedAt,
            SubmittedBy = entity.SubmittedBy,
            AdditionalContactEmails = entity.AdditionalContactEmails,
            TariffSubmissionOrigin = entity.TariffSubmissionOrigin,
            OriginalLanguage = entity.OriginalLanguage,
            Currency = entity.Currency,
            PreferentialTariffs = entity.PreferentialTariffs,
            BeneficiaryList = entity.BeneficiaryList,
            OtherDutiesAndCharges = entity.OtherDutiesAndCharges,
            DutiesApplicableFrom = entity.DutiesApplicableFrom,
            DutiesApplicableTo = entity.DutiesApplicableTo,
            Description = entity.Description,
            CreatedAt = entity.CreatedAt,
            ModifiedAt = entity.ModifiedAt,
            CreatedBy = entity.CreatedBy,
            ModifiedBy = entity.ModifiedBy
        };
    }
}
