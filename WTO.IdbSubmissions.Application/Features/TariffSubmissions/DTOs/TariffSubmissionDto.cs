using WTO.IdbSubmissions.Application.Common.Models;
using WTO.IdbSubmissions.Domain.Common;

namespace WTO.IdbSubmissions.Application.Features.TariffSubmissions.DTOs;

/// <summary>
/// Data Transfer Object for TariffSubmission entity
/// </summary>
public class TariffSubmissionDto : BaseDto
{
    /// <summary>
    /// WTO Country Code (ISO 3166-1 alpha-3)
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Year of the tariff submission
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// Current status of the submission
    /// </summary>
    public SubmissionStatus Status { get; set; }

    /// <summary>
    /// Date and time when the submission was submitted
    /// </summary>
    public DateTime? SubmittedAt { get; set; }

    /// <summary>
    /// User who submitted the tariff submission
    /// </summary>
    public string? SubmittedBy { get; set; }

    /// <summary>
    /// Additional contact emails for the submission
    /// </summary>
    public string? AdditionalContactEmails { get; set; }

    /// <summary>
    /// Origin of the tariff submission
    /// </summary>
    public TariffSubmissionOrigin TariffSubmissionOrigin { get; set; }

    /// <summary>
    /// Original language of the submission
    /// </summary>
    public WTOLanguage OriginalLanguage { get; set; }

    /// <summary>
    /// Currency used in the tariff submission
    /// </summary>
    public string Currency { get; set; } = string.Empty;

    /// <summary>
    /// Indicates if preferential tariffs are included
    /// </summary>
    public bool PreferentialTariffs { get; set; }

    /// <summary>
    /// Indicates if beneficiary list is included
    /// </summary>
    public bool BeneficiaryList { get; set; }

    /// <summary>
    /// Indicates if other duties and charges are included
    /// </summary>
    public bool OtherDutiesAndCharges { get; set; }

    /// <summary>
    /// Date from which the duties are applicable
    /// </summary>
    public DateOnly DutiesApplicableFrom { get; set; }

    /// <summary>
    /// Date until which the duties are applicable
    /// </summary>
    public DateOnly DutiesApplicableTo { get; set; }

    /// <summary>
    /// Additional description or notes for the submission
    /// </summary>
    public string? Description { get; set; }
}
