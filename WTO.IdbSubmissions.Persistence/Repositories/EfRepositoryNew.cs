using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Persistence.Data;

namespace WTO.IdbSubmissions.Persistence.Repositories;

/// <summary>
/// Entity Framework Core repository implementation
/// </summary>
/// <typeparam name="T">Entity type that inherits from BaseEntity</typeparam>
public class EfRepository<T> : IRepository<T> where T : BaseEntity
{
    private readonly IdbSubmissionsDbContext _context;
    private readonly DbSet<T> _dbSet;

    /// <summary>
    /// Initializes a new instance of the EfRepository class
    /// </summary>
    /// <param name="context">Database context</param>
    public EfRepository(IdbSubmissionsDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _dbSet = _context.Set<T>();
    }

    /// <summary>
    /// Gets an entity by its ID
    /// </summary>
    /// <param name="id">Entity ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Entity if found, null otherwise</returns>
    public async Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FindAsync(new object[] { id }, cancellationToken);
    }

    /// <summary>
    /// Gets all entities
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of all entities</returns>
    public async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet.ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Gets all entities matching a predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of entities matching the predicate</returns>
    public async Task<IEnumerable<T>> GetAllAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(predicate).ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Finds entities matching the specified predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of entities matching the predicate</returns>
    public async Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(predicate).ToListAsync(cancellationToken);
    }

    /// <summary>
    /// Gets the first entity matching the specified predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>First entity matching the predicate, or null if not found</returns>
    public async Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(predicate, cancellationToken);
    }

    /// <summary>
    /// Gets the single entity matching the specified predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Single entity matching the predicate</returns>
    /// <exception cref="InvalidOperationException">Thrown when no entity or more than one entity matches the predicate</exception>
    public async Task<T> SingleAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.SingleAsync(predicate, cancellationToken);
    }

    /// <summary>
    /// Gets the single entity matching the specified predicate, or null if not found
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Single entity matching the predicate, or null if not found</returns>
    /// <exception cref="InvalidOperationException">Thrown when more than one entity matches the predicate</exception>
    public async Task<T?> SingleOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.SingleOrDefaultAsync(predicate, cancellationToken);
    }

    /// <summary>
    /// Checks if any entity matches the specified predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if any entity matches the predicate, false otherwise</returns>
    public async Task<bool> AnyAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default)
    {
        return await _dbSet.AnyAsync(predicate, cancellationToken);
    }

    /// <summary>
    /// Counts entities matching the specified predicate
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of entities matching the predicate</returns>
    public async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default)
    {
        if (predicate == null)
            return await _dbSet.CountAsync(cancellationToken);
        
        return await _dbSet.CountAsync(predicate, cancellationToken);
    }

    /// <summary>
    /// Adds a new entity
    /// </summary>
    /// <param name="entity">The entity to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The added entity</returns>
    public async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        var entry = await _dbSet.AddAsync(entity, cancellationToken);
        return entry.Entity;
    }

    /// <summary>
    /// Adds multiple entities
    /// </summary>
    /// <param name="entities">The entities to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public async Task AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities));

        await _dbSet.AddRangeAsync(entities, cancellationToken);
    }

    /// <summary>
    /// Updates an existing entity
    /// </summary>
    /// <param name="entity">The entity to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public Task UpdateAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        _dbSet.Update(entity);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Updates multiple entities
    /// </summary>
    /// <param name="entities">The entities to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public Task UpdateRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities));

        _dbSet.UpdateRange(entities);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Removes an entity (hard delete)
    /// </summary>
    /// <param name="entity">The entity to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public Task RemoveAsync(T entity, CancellationToken cancellationToken = default)
    {
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        _dbSet.Remove(entity);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Removes multiple entities (hard delete)
    /// </summary>
    /// <param name="entities">The entities to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public Task RemoveRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default)
    {
        if (entities == null)
            throw new ArgumentNullException(nameof(entities));

        _dbSet.RemoveRange(entities);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Soft deletes an entity
    /// </summary>
    /// <param name="id">The entity identifier</param>
    /// <param name="deletedBy">User performing the deletion</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public async Task SoftDeleteAsync(Guid id, string? deletedBy = null, CancellationToken cancellationToken = default)
    {
        var entity = await GetByIdAsync(id, cancellationToken);
        if (entity != null)
        {
            entity.SetDeleted(deletedBy);
            await UpdateAsync(entity, cancellationToken);
        }
    }

    /// <summary>
    /// Gets a paginated list of entities matching a predicate with sorting
    /// </summary>
    /// <param name="predicate">The search predicate</param>
    /// <param name="skip">Number of items to skip</param>
    /// <param name="take">Number of items to take</param>
    /// <param name="orderBy">Sort expression</param>
    /// <param name="descending">Whether to sort in descending order</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated collection of entities</returns>
    public async Task<IEnumerable<T>> GetPagedAsync(
        Expression<Func<T, bool>> predicate,
        int skip,
        int take,
        Expression<Func<T, object>> orderBy,
        bool descending = false,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(predicate);
        
        if (descending)
        {
            query = query.OrderByDescending(orderBy);
        }
        else
        {
            query = query.OrderBy(orderBy);
        }
        
        return await query.Skip(skip).Take(take).ToListAsync(cancellationToken);
    }
}
