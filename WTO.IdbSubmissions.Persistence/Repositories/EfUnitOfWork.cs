using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Persistence.Common;
using WTO.IdbSubmissions.Persistence.Data;

namespace WTO.IdbSubmissions.Persistence.Repositories;

/// <summary>
/// Entity Framework Core Unit of Work implementation
/// </summary>
public class EfUnitOfWork : UnitOfWork
{
    private readonly IdbSubmissionsDbContext _context;
    private readonly Dictionary<Type, object> _repositories = new();
    private readonly object _lock = new();

    /// <summary>
    /// Initializes a new instance of the EfUnitOfWork class
    /// </summary>
    /// <param name="context">Database context</param>
    public EfUnitOfWork(IdbSubmissionsDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Saves all changes made in this unit of work to the underlying data store
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The number of state entries written to the underlying data store</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Begins a new transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Transaction object</returns>
    public override async Task<ITransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        return new EfTransaction(transaction);
    }

    /// <summary>
    /// Discards all changes made in this unit of work
    /// </summary>
    public override void RejectChanges()
    {
        foreach (var entry in _context.ChangeTracker.Entries())
        {
            switch (entry.State)
            {
                case EntityState.Modified:
                case EntityState.Deleted:
                    entry.State = EntityState.Modified;
                    entry.State = EntityState.Unchanged;
                    break;
                case EntityState.Added:
                    entry.State = EntityState.Detached;
                    break;
            }
        }
    }

    /// <summary>
    /// Sets the current user for auditing purposes
    /// </summary>
    /// <param name="currentUser">The current user identifier</param>
    public void SetCurrentUser(string? currentUser)
    {
        _context.SetCurrentUser(currentUser);
    }

    /// <summary>
    /// Creates a repository instance for the specified entity type
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <returns>Repository instance</returns>
    protected override IRepository<T> CreateRepository<T>()
    {
        lock (_lock)
        {
            var type = typeof(T);
            
            if (_repositories.ContainsKey(type))
            {
                return (IRepository<T>)_repositories[type];
            }

            var repository = new EfRepository<T>(_context);
            _repositories.Add(type, repository);
            return repository;
        }
    }

    /// <summary>
    /// Disposes the unit of work
    /// </summary>
    /// <param name="disposing">Whether disposing is in progress</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            lock (_lock)
            {
                _repositories.Clear();
            }
            _context?.Dispose();
        }
        base.Dispose(disposing);
    }
}

/// <summary>
/// Entity Framework Core transaction wrapper
/// </summary>
public class EfTransaction : ITransaction
{
    private readonly IDbContextTransaction _transaction;
    private bool _disposed = false;

    /// <summary>
    /// Initializes a new instance of the EfTransaction class
    /// </summary>
    /// <param name="transaction">EF Core transaction</param>
    public EfTransaction(IDbContextTransaction transaction)
    {
        _transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
    }

    /// <summary>
    /// Commits the transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.CommitAsync(cancellationToken);
    }

    /// <summary>
    /// Rolls back the transaction
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the operation</returns>
    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        await _transaction.RollbackAsync(cancellationToken);
    }

    /// <summary>
    /// Disposes the transaction
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Disposes the transaction
    /// </summary>
    /// <param name="disposing">Whether disposing is in progress</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _transaction?.Dispose();
            _disposed = true;
        }
    }
}
