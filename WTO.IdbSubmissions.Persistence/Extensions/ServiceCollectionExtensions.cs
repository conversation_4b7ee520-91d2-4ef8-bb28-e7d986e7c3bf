using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WTO.IdbSubmissions.Application.Common.Interfaces;
using WTO.IdbSubmissions.Persistence.Data;
using WTO.IdbSubmissions.Persistence.Repositories;

namespace WTO.IdbSubmissions.Persistence.Extensions;

/// <summary>
/// Extension methods for configuring persistence services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Entity Framework Core persistence services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPersistence(this IServiceCollection services, IConfiguration configuration)
    {
        // Add DbContext
        services.AddDbContext<IdbSubmissionsDbContext>(options =>
        {
            options.UseSqlServer(
                configuration.GetConnectionString("DefaultConnection"),
                sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 3,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorNumbersToAdd: null);
                    
                    // Set command timeout to 30 seconds
                    sqlOptions.CommandTimeout(30);
                });

            // Enable sensitive data logging in development
            if (configuration.GetValue<bool>("Logging:EnableSensitiveDataLogging"))
            {
                options.EnableSensitiveDataLogging();
            }

            // Enable detailed errors in development
            if (configuration.GetValue<bool>("Logging:EnableDetailedErrors"))
            {
                options.EnableDetailedErrors();
            }
        });

        // Add Unit of Work
        services.AddScoped<IUnitOfWork, EfUnitOfWork>();

        return services;
    }

    /// <summary>
    /// Adds in-memory persistence services to the service collection (for testing)
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddInMemoryPersistence(this IServiceCollection services)
    {
        // Add in-memory DbContext
        services.AddDbContext<IdbSubmissionsDbContext>(options =>
        {
            options.UseInMemoryDatabase("IdbSubmissionsTestDb");
            options.EnableSensitiveDataLogging();
            options.EnableDetailedErrors();
        });

        // Add Unit of Work
        services.AddScoped<IUnitOfWork, EfUnitOfWork>();

        return services;
    }
}
