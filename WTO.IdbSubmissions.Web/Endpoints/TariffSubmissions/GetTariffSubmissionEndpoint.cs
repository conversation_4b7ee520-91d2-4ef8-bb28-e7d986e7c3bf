using FastEndpoints;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;
using WTO.IdbSubmissions.Web.Authorization;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.TariffSubmissions;

/// <summary>
/// Request DTO for getting a tariff submission
/// </summary>
public class GetTariffSubmissionRequest
{
    /// <summary>
    /// Tariff submission ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Whether to include soft-deleted submissions
    /// </summary>
    public bool IncludeDeleted { get; set; } = false;
}

/// <summary>
/// Response DTO for getting a tariff submission
/// </summary>
public class GetTariffSubmissionResponseDto : GetTariffSubmissionResponse
{
    // Inherits all properties from the response
}

/// <summary>
/// Endpoint for getting a tariff submission by ID
/// </summary>
public class GetTariffSubmissionEndpoint : BaseEndpoint<GetTariffSubmissionRequest, GetTariffSubmissionResponseDto>
{
    private readonly GetTariffSubmissionHandler _handler;

    /// <summary>
    /// Initializes a new instance of the GetTariffSubmissionEndpoint class
    /// </summary>
    /// <param name="handler">Get tariff submission handler</param>
    public GetTariffSubmissionEndpoint(GetTariffSubmissionHandler handler)
    {
        _handler = handler ?? throw new ArgumentNullException(nameof(handler));
    }

    /// <summary>
    /// Configures the endpoint
    /// </summary>
    public override void Configure()
    {
        Get("/api/tariff-submissions/{id}");
        Policies(AuthorizationPolicies.IdbUserPolicy);
        Summary(s =>
        {
            s.Summary = "Get a tariff submission by ID";
            s.Description = "Retrieves a specific tariff submission by its unique identifier";
            s.ExampleRequest = new GetTariffSubmissionRequest
            {
                Id = Guid.NewGuid(),
                IncludeDeleted = false
            };
            s.Responses[200] = "Tariff submission retrieved successfully";
            s.Responses[404] = "Tariff submission not found";
        });
    }

    /// <summary>
    /// Handles the get tariff submission request
    /// </summary>
    /// <param name="req">The request</param>
    /// <param name="ct">Cancellation token</param>
    public override async Task HandleAsync(GetTariffSubmissionRequest req, CancellationToken ct)
    {
        var query = new GetTariffSubmissionQuery
        {
            Id = req.Id,
            IncludeDeleted = req.IncludeDeleted
        };

        var result = await _handler.HandleAsync(query, ct);

        if (!result.IsSuccess && (result.ErrorMessage?.Contains("not found") == true || result.Errors.Any(e => e.Contains("not found"))))
        {
            await SendErrorAsync(result.ErrorMessage ?? "Tariff submission not found", result.Errors.ToList(), 404, ct);
        }
        else
        {
            await HandleResultAsync(result, "Tariff submission retrieved successfully", ct);
        }
    }
}
