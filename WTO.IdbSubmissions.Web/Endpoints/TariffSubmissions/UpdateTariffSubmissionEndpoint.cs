using FastEndpoints;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;
using WTO.IdbSubmissions.Web.Authorization;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.TariffSubmissions;

/// <summary>
/// Request DTO for updating a tariff submission
/// </summary>
public class UpdateTariffSubmissionRequest : UpdateTariffSubmissionCommand
{
    // Inherits all properties from the command
}

/// <summary>
/// Response DTO for updating a tariff submission
/// </summary>
public class UpdateTariffSubmissionResponseDto : UpdateTariffSubmissionResponse
{
    // Inherits all properties from the response
}

/// <summary>
/// Endpoint for updating an existing tariff submission
/// </summary>
public class UpdateTariffSubmissionEndpoint : BaseEndpoint<UpdateTariffSubmissionRequest, UpdateTariffSubmissionResponseDto>
{
    private readonly UpdateTariffSubmissionHandler _handler;

    /// <summary>
    /// Initializes a new instance of the UpdateTariffSubmissionEndpoint class
    /// </summary>
    /// <param name="handler">Update tariff submission handler</param>
    public UpdateTariffSubmissionEndpoint(UpdateTariffSubmissionHandler handler)
    {
        _handler = handler ?? throw new ArgumentNullException(nameof(handler));
    }

    /// <summary>
    /// Configures the endpoint
    /// </summary>
    public override void Configure()
    {
        Put("/api/tariff-submissions/{id}");
        Policies(AuthorizationPolicies.IdbAdminPolicy);
        Summary(s =>
        {
            s.Summary = "Update an existing tariff submission";
            s.Description = "Updates an existing tariff submission with the provided information. Only Draft and Submitted submissions can be updated.";
            s.ExampleRequest = new UpdateTariffSubmissionRequest
            {
                Id = Guid.NewGuid(),
                WTOCountryCode = "USA",
                Year = 2024,
                Currency = "USD",
                TariffSubmissionOrigin = Domain.Common.TariffSubmissionOrigin.Government,
                OriginalLanguage = Domain.Common.WTOLanguage.English,
                PreferentialTariffs = true,
                BeneficiaryList = false,
                OtherDutiesAndCharges = true,
                DutiesApplicableFrom = new DateOnly(2024, 1, 1),
                DutiesApplicableTo = new DateOnly(2024, 12, 31),
                Description = "Updated annual tariff submission for 2024",
                AdditionalContactEmails = "<EMAIL>",
                ModifiedBy = "<EMAIL>"
            };
            s.Responses[200] = "Tariff submission updated successfully";
            s.Responses[400] = "Invalid request data";
            s.Responses[404] = "Tariff submission not found";
            s.Responses[409] = "Cannot update tariff submission in current status";
        });
    }

    /// <summary>
    /// Handles the update tariff submission request
    /// </summary>
    /// <param name="req">The request</param>
    /// <param name="ct">Cancellation token</param>
    public override async Task HandleAsync(UpdateTariffSubmissionRequest req, CancellationToken ct)
    {
        // Set the modified by from the current user context
        req.ModifiedBy = User?.Identity?.Name;

        // Create the command from the request
        var command = new UpdateTariffSubmissionCommand
        {
            Id = req.Id,
            WTOCountryCode = req.WTOCountryCode,
            Year = req.Year,
            AdditionalContactEmails = req.AdditionalContactEmails,
            TariffSubmissionOrigin = req.TariffSubmissionOrigin,
            OriginalLanguage = req.OriginalLanguage,
            Currency = req.Currency,
            PreferentialTariffs = req.PreferentialTariffs,
            BeneficiaryList = req.BeneficiaryList,
            OtherDutiesAndCharges = req.OtherDutiesAndCharges,
            DutiesApplicableFrom = req.DutiesApplicableFrom,
            DutiesApplicableTo = req.DutiesApplicableTo,
            Description = req.Description,
            ModifiedBy = req.ModifiedBy
        };

        var result = await _handler.HandleAsync(command, ct);

        if (!result.IsSuccess)
        {
            if (result.ErrorMessage?.Contains("not found") == true)
            {
                await SendErrorAsync(result.ErrorMessage, result.Errors.ToList(), 404, ct);
            }
            else if (result.ErrorMessage?.Contains("Cannot update") == true || result.ErrorMessage?.Contains("status") == true)
            {
                await SendErrorAsync(result.ErrorMessage, result.Errors.ToList(), 409, ct);
            }
            else
            {
                await SendErrorAsync(result.ErrorMessage ?? "Failed to update tariff submission", result.Errors.ToList(), 400, ct);
            }
        }
        else
        {
            await HandleResultAsync(result, "Tariff submission updated successfully", ct);
        }
    }
}
