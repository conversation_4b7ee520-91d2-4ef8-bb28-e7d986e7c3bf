using FastEndpoints;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;
using WTO.IdbSubmissions.Web.Authorization;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.TariffSubmissions;

/// <summary>
/// Request DTO for submitting a tariff submission
/// </summary>
public class SubmitTariffSubmissionRequest
{
    /// <summary>
    /// ID of the tariff submission to submit
    /// </summary>
    public Guid Id { get; set; }
}

/// <summary>
/// Response DTO for submitting a tariff submission
/// </summary>
public class SubmitTariffSubmissionResponseDto : SubmitTariffSubmissionResponse
{
    // Inherits all properties from the response
}

/// <summary>
/// Endpoint for submitting a tariff submission (changing status from Draft to Submitted)
/// </summary>
public class SubmitTariffSubmissionEndpoint : BaseEndpoint<SubmitTariffSubmissionRequest, SubmitTariffSubmissionResponseDto>
{
    private readonly SubmitTariffSubmissionHandler _handler;

    /// <summary>
    /// Initializes a new instance of the SubmitTariffSubmissionEndpoint class
    /// </summary>
    /// <param name="handler">Submit tariff submission handler</param>
    public SubmitTariffSubmissionEndpoint(SubmitTariffSubmissionHandler handler)
    {
        _handler = handler ?? throw new ArgumentNullException(nameof(handler));
    }

    /// <summary>
    /// Configures the endpoint
    /// </summary>
    public override void Configure()
    {
        Post("/api/tariff-submissions/{id}/submit");
        Policies(AuthorizationPolicies.IdbAdminPolicy);
        Summary(s =>
        {
            s.Summary = "Submit a tariff submission";
            s.Description = "Changes the status of a tariff submission from Draft to Submitted. The submission must be complete and valid.";
            s.ExampleRequest = new SubmitTariffSubmissionRequest
            {
                Id = Guid.NewGuid()
            };
            s.Responses[200] = "Tariff submission submitted successfully";
            s.Responses[400] = "Invalid request or submission not ready for submission";
            s.Responses[404] = "Tariff submission not found";
            s.Responses[409] = "Cannot submit tariff submission in current status";
        });
    }

    /// <summary>
    /// Handles the submit tariff submission request
    /// </summary>
    /// <param name="req">The request</param>
    /// <param name="ct">Cancellation token</param>
    public override async Task HandleAsync(SubmitTariffSubmissionRequest req, CancellationToken ct)
    {
        var command = new SubmitTariffSubmissionCommand
        {
            Id = req.Id,
            SubmittedBy = User?.Identity?.Name
        };

        var result = await _handler.HandleAsync(command, ct);

        if (!result.IsSuccess)
        {
            if (result.ErrorMessage?.Contains("not found") == true)
            {
                await SendErrorAsync(result.ErrorMessage, result.Errors.ToList(), 404, ct);
            }
            else if (result.ErrorMessage?.Contains("Cannot submit") == true || result.ErrorMessage?.Contains("status") == true)
            {
                await SendErrorAsync(result.ErrorMessage, result.Errors.ToList(), 409, ct);
            }
            else
            {
                await SendErrorAsync(result.ErrorMessage ?? "Failed to submit tariff submission", result.Errors.ToList(), 400, ct);
            }
        }
        else
        {
            await HandleResultAsync(result, "Tariff submission submitted successfully", ct);
        }
    }
}
