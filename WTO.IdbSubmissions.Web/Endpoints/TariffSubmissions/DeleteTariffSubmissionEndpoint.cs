using FastEndpoints;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;
using WTO.IdbSubmissions.Web.Authorization;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.TariffSubmissions;

/// <summary>
/// Request DTO for deleting a tariff submission
/// </summary>
public class DeleteTariffSubmissionRequest
{
    /// <summary>
    /// ID of the tariff submission to delete
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Whether to perform a hard delete instead of soft delete
    /// </summary>
    public bool HardDelete { get; set; } = false;
}

/// <summary>
/// Response DTO for deleting a tariff submission
/// </summary>
public class DeleteTariffSubmissionResponseDto : DeleteTariffSubmissionResponse
{
    // Inherits all properties from the response
}

/// <summary>
/// Endpoint for deleting a tariff submission
/// </summary>
public class DeleteTariffSubmissionEndpoint : BaseEndpoint<DeleteTariffSubmissionRequest, DeleteTariffSubmissionResponseDto>
{
    private readonly DeleteTariffSubmissionHandler _handler;

    /// <summary>
    /// Initializes a new instance of the DeleteTariffSubmissionEndpoint class
    /// </summary>
    /// <param name="handler">Delete tariff submission handler</param>
    public DeleteTariffSubmissionEndpoint(DeleteTariffSubmissionHandler handler)
    {
        _handler = handler ?? throw new ArgumentNullException(nameof(handler));
    }

    /// <summary>
    /// Configures the endpoint
    /// </summary>
    public override void Configure()
    {
        Delete("/api/tariff-submissions/{id}");
        Policies(AuthorizationPolicies.IdbAdminPolicy);
        Summary(s =>
        {
            s.Summary = "Delete a tariff submission";
            s.Description = "Deletes a tariff submission. By default performs a soft delete. Finalized submissions cannot be deleted.";
            s.ExampleRequest = new DeleteTariffSubmissionRequest
            {
                Id = Guid.NewGuid(),
                HardDelete = false
            };
            s.Responses[200] = "Tariff submission deleted successfully";
            s.Responses[404] = "Tariff submission not found";
            s.Responses[409] = "Cannot delete tariff submission in current status";
        });
    }

    /// <summary>
    /// Handles the delete tariff submission request
    /// </summary>
    /// <param name="req">The request</param>
    /// <param name="ct">Cancellation token</param>
    public override async Task HandleAsync(DeleteTariffSubmissionRequest req, CancellationToken ct)
    {
        var command = new DeleteTariffSubmissionCommand
        {
            Id = req.Id,
            HardDelete = req.HardDelete,
            DeletedBy = User?.Identity?.Name
        };

        var result = await _handler.HandleAsync(command, ct);

        if (!result.IsSuccess)
        {
            if (result.ErrorMessage?.Contains("not found") == true)
            {
                await SendErrorAsync(result.ErrorMessage, result.Errors.ToList(), 404, ct);
            }
            else if (result.ErrorMessage?.Contains("Cannot delete") == true || result.ErrorMessage?.Contains("finalized") == true)
            {
                await SendErrorAsync(result.ErrorMessage, result.Errors.ToList(), 409, ct);
            }
            else
            {
                await SendErrorAsync(result.ErrorMessage ?? "Failed to delete tariff submission", result.Errors.ToList(), 400, ct);
            }
        }
        else
        {
            await HandleResultAsync(result, "Tariff submission deleted successfully", ct);
        }
    }
}
