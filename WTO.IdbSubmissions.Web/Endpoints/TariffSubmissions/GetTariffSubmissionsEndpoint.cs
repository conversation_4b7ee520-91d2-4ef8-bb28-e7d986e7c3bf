using FastEndpoints;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries;
using WTO.IdbSubmissions.Domain.Common;
using WTO.IdbSubmissions.Web.Authorization;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.TariffSubmissions;

/// <summary>
/// Request DTO for getting tariff submissions list
/// </summary>
public class GetTariffSubmissionsRequest
{
    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int PageNumber { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Filter by WTO Country Code
    /// </summary>
    public string? CountryCode { get; set; }

    /// <summary>
    /// Filter by year
    /// </summary>
    public int? Year { get; set; }

    /// <summary>
    /// Filter by status
    /// </summary>
    public SubmissionStatus? Status { get; set; }

    /// <summary>
    /// Filter by submission origin
    /// </summary>
    public TariffSubmissionOrigin? Origin { get; set; }

    /// <summary>
    /// Filter by original language
    /// </summary>
    public WTOLanguage? Language { get; set; }

    /// <summary>
    /// Search term for description or country code
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Whether to include soft-deleted submissions
    /// </summary>
    public bool IncludeDeleted { get; set; } = false;

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "CreatedAt";

    /// <summary>
    /// Sort direction (asc/desc)
    /// </summary>
    public string SortDirection { get; set; } = "desc";
}

/// <summary>
/// Response DTO for getting tariff submissions list
/// </summary>
public class GetTariffSubmissionsResponseDto : GetTariffSubmissionsResponse
{
    // Inherits all properties from the response
}

/// <summary>
/// Endpoint for getting a paginated list of tariff submissions
/// </summary>
public class GetTariffSubmissionsEndpoint : BaseEndpoint<GetTariffSubmissionsRequest, GetTariffSubmissionsResponseDto>
{
    private readonly GetTariffSubmissionsHandler _handler;

    /// <summary>
    /// Initializes a new instance of the GetTariffSubmissionsEndpoint class
    /// </summary>
    /// <param name="handler">Get tariff submissions handler</param>
    public GetTariffSubmissionsEndpoint(GetTariffSubmissionsHandler handler)
    {
        _handler = handler ?? throw new ArgumentNullException(nameof(handler));
    }

    /// <summary>
    /// Configures the endpoint
    /// </summary>
    public override void Configure()
    {
        Get("/api/tariff-submissions");
        Policies(AuthorizationPolicies.IdbUserPolicy);
        Summary(s =>
        {
            s.Summary = "Get a paginated list of tariff submissions";
            s.Description = "Retrieves a paginated list of tariff submissions with optional filtering and sorting";
            s.ExampleRequest = new GetTariffSubmissionsRequest
            {
                PageNumber = 1,
                PageSize = 10,
                CountryCode = "USA",
                Year = 2024,
                Status = SubmissionStatus.Draft,
                SortBy = "CreatedAt",
                SortDirection = "desc"
            };
            s.Responses[200] = "Tariff submissions retrieved successfully";
            s.Responses[400] = "Invalid request parameters";
        });
    }

    /// <summary>
    /// Handles the get tariff submissions request
    /// </summary>
    /// <param name="req">The request</param>
    /// <param name="ct">Cancellation token</param>
    public override async Task HandleAsync(GetTariffSubmissionsRequest req, CancellationToken ct)
    {
        var query = new GetTariffSubmissionsQuery
        {
            PageNumber = req.PageNumber,
            PageSize = req.PageSize,
            CountryCode = req.CountryCode,
            Year = req.Year,
            Status = req.Status,
            Origin = req.Origin,
            Language = req.Language,
            SearchTerm = req.SearchTerm,
            IncludeDeleted = req.IncludeDeleted,
            SortBy = req.SortBy,
            SortDirection = req.SortDirection
        };

        var result = await _handler.HandleAsync(query, ct);
        await HandleResultAsync(result, "Tariff submissions retrieved successfully", ct);
    }
}
