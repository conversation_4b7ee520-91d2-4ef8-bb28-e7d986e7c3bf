using FastEndpoints;
using WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands;
using WTO.IdbSubmissions.Web.Authorization.Constants;
using WTO.IdbSubmissions.Web.Endpoints.Common;

namespace WTO.IdbSubmissions.Web.Endpoints.TariffSubmissions;

/// <summary>
/// Request DTO for creating a tariff submission
/// </summary>
public class CreateTariffSubmissionRequest : CreateTariffSubmissionCommand
{
    // Inherits all properties from the command
}

/// <summary>
/// Response DTO for creating a tariff submission
/// </summary>
public class CreateTariffSubmissionResponseDto
{
    /// <summary>
    /// Created tariff submission's ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// WTO Country Code
    /// </summary>
    public string WTOCountryCode { get; set; } = string.Empty;

    /// <summary>
    /// Year of the submission
    /// </summary>
    public int Year { get; set; }

    /// <summary>
    /// Current status of the submission
    /// </summary>
    public Domain.Common.SubmissionStatus Status { get; set; }

    /// <summary>
    /// Date when the submission was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// User who created the submission
    /// </summary>
    public string? CreatedBy { get; set; }
}

/// <summary>
/// Endpoint for creating a new tariff submission
/// </summary>
public class CreateTariffSubmissionEndpoint : BaseEndpoint<CreateTariffSubmissionRequest, CreateTariffSubmissionResponseDto>
{
    private readonly CreateTariffSubmissionHandler _handler;

    /// <summary>
    /// Initializes a new instance of the CreateTariffSubmissionEndpoint class
    /// </summary>
    /// <param name="handler">Create tariff submission handler</param>
    public CreateTariffSubmissionEndpoint(CreateTariffSubmissionHandler handler)
    {
        _handler = handler ?? throw new ArgumentNullException(nameof(handler));
    }

    /// <summary>
    /// Configures the endpoint
    /// </summary>
    public override void Configure()
    {
        Post("/api/tariff-submissions");
        Policies(AuthorizationPolicies.IdbAdminPolicy);
        Summary(s =>
        {
            s.Summary = "Create a new tariff submission";
            s.Description = "Creates a new tariff submission with the provided information";
            s.ExampleRequest = new CreateTariffSubmissionRequest
            {
                WTOCountryCode = "USA",
                Year = 2024,
                Currency = "USD",
                TariffSubmissionOrigin = Domain.Common.TariffSubmissionOrigin.Government,
                OriginalLanguage = Domain.Common.WTOLanguage.English,
                PreferentialTariffs = true,
                BeneficiaryList = false,
                OtherDutiesAndCharges = true,
                DutiesApplicableFrom = new DateOnly(2024, 1, 1),
                DutiesApplicableTo = new DateOnly(2024, 12, 31),
                Description = "Annual tariff submission for 2024",
                AdditionalContactEmails = "<EMAIL>",
                CreatedBy = "<EMAIL>"
            };
            s.Responses[200] = "Tariff submission created successfully";
            s.Responses[400] = "Invalid request data";
            s.Responses[409] = "Tariff submission already exists for this country and year";
        });
    }

    /// <summary>
    /// Handles the create tariff submission request
    /// </summary>
    /// <param name="req">The request</param>
    /// <param name="ct">Cancellation token</param>
    public override async Task HandleAsync(CreateTariffSubmissionRequest req, CancellationToken ct)
    {
        // Set the created by from the current user context
        req.CreatedBy = User?.Identity?.Name;

        // Create the command from the request
        var command = new CreateTariffSubmissionCommand
        {
            WTOCountryCode = req.WTOCountryCode,
            Year = req.Year,
            AdditionalContactEmails = req.AdditionalContactEmails,
            TariffSubmissionOrigin = req.TariffSubmissionOrigin,
            OriginalLanguage = req.OriginalLanguage,
            Currency = req.Currency,
            PreferentialTariffs = req.PreferentialTariffs,
            BeneficiaryList = req.BeneficiaryList,
            OtherDutiesAndCharges = req.OtherDutiesAndCharges,
            DutiesApplicableFrom = req.DutiesApplicableFrom,
            DutiesApplicableTo = req.DutiesApplicableTo,
            Description = req.Description,
            CreatedBy = req.CreatedBy
        };

        var result = await _handler.HandleAsync(command, ct);

        if (!result.IsSuccess)
        {
            if (result.ErrorMessage?.Contains("already exists") == true)
            {
                await SendErrorAsync(result.ErrorMessage, result.Errors.ToList(), 409, ct);
            }
            else
            {
                await SendErrorAsync(result.ErrorMessage ?? "Failed to create tariff submission", result.Errors.ToList(), 400, ct);
            }
        }
        else
        {
            // Map the response
            var response = new CreateTariffSubmissionResponseDto
            {
                Id = result.Data!.Id,
                WTOCountryCode = result.Data.WTOCountryCode,
                Year = result.Data.Year,
                Status = result.Data.Status,
                CreatedAt = result.Data.CreatedAt,
                CreatedBy = result.Data.CreatedBy
            };

            await SendSuccessAsync(response, "Tariff submission created successfully", ct);
        }
    }
}
