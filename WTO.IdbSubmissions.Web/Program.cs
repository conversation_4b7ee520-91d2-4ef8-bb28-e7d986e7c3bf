using FastEndpoints;
using FastEndpoints.Swagger;
using Serilog;
using WTO.IdbSubmissions.Infrastructure.Services;
using WTO.IdbSubmissions.Persistence.Extensions;
using WTO.IdbSubmissions.Web.Extensions;
using WTO.IdbSubmissions.Web.Testing;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .Enrich.WithMachineName()
    .Enrich.WithProcessId()
    .Enrich.WithThreadId()
    .Enrich.WithEnvironmentName()
    .CreateLogger();

try
{
    Log.Information("Starting WTO IDB Submissions application");

    // Use Serilog for logging
    builder.Host.UseSerilog();

// Add services to the container.
builder.Services.AddControllersWithViews();

// Add Authentication and Authorization
if (builder.Environment.EnvironmentName != "Testing")
{
    builder.Services.AddWTOADFSAuthentication(builder.Configuration);
    builder.Services.AddIdbAuthorization();
}
else
{
    // For testing, use simple authentication
    builder.Services.AddAuthentication("Test")
        .AddScheme<Microsoft.AspNetCore.Authentication.AuthenticationSchemeOptions, TestAuthenticationHandler>("Test", options => { });
    builder.Services.AddAuthorization();
}

// Add FastEndpoints
builder.Services.AddFastEndpoints()
    .SwaggerDocument(o =>
    {
        o.DocumentSettings = s =>
        {
            s.Title = "WTO IDB Submissions API";
            s.Version = "v1";
        };
    });

// Add Clean Architecture services
builder.Services.AddDomainEventPublisher();

// Add Repository and UnitOfWork (In-Memory implementation for now)
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Common.Interfaces.IUnitOfWork, WTO.IdbSubmissions.Infrastructure.Repositories.InMemoryUnitOfWork>();

// Add Application handlers
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.Users.Commands.CreateUserHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.Users.Queries.GetUserHandler>();

// Add TariffSubmission handlers
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands.CreateTariffSubmissionHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands.UpdateTariffSubmissionHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands.DeleteTariffSubmissionHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands.SubmitTariffSubmissionHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands.AcceptTariffSubmissionHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Commands.FinalizeTariffSubmissionHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries.GetTariffSubmissionHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries.GetTariffSubmissionsHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries.GetTariffSubmissionsByCountryHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries.GetTariffSubmissionsByStatusHandler>();
builder.Services.AddScoped<WTO.IdbSubmissions.Application.Features.TariffSubmissions.Queries.GetTariffSubmissionsByYearHandler>();

// TODO: Add Entity Framework Core when needed
// TODO: Add AutoMapper when needed
// TODO: Add FluentValidation when needed

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

// Add Serilog request logging
app.UseSerilogRequestLogging(options =>
{
    // Customize the message template
    options.MessageTemplate = "Handled {RequestMethod} {RequestPath} responded {StatusCode} in {Elapsed:0.0000} ms";

    // Enrich the log with additional properties
    options.EnrichDiagnosticContext = (diagnosticContext, httpContext) =>
    {
        diagnosticContext.Set("RequestHost", httpContext.Request.Host.Value ?? "Unknown");
        diagnosticContext.Set("RequestScheme", httpContext.Request.Scheme);
        diagnosticContext.Set("UserAgent", httpContext.Request.Headers.UserAgent.FirstOrDefault() ?? "Unknown");

        if (httpContext.User.Identity?.IsAuthenticated == true)
        {
            diagnosticContext.Set("UserName", httpContext.User.Identity.Name ?? "Unknown");
        }
    };
});

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

// Configure access denied path
app.Use(async (context, next) =>
{
    await next();

    if (context.Response.StatusCode == 403)
    {
        context.Request.Path = "/Account/AccessDenied";
        await next();
    }
});

// Configure FastEndpoints
app.UseFastEndpoints(c =>
{
    c.Endpoints.RoutePrefix = "api";
})
.UseSwaggerGen(); // FastEndpoints Swagger integration

app.MapStaticAssets();

// Map MVC routes
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}")
    .WithStaticAssets();

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.Information("Shutting down WTO IDB Submissions application");
    Log.CloseAndFlush();
}

// Make Program class accessible for integration tests
public partial class Program { }
